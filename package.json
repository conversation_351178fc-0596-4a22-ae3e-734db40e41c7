{"name": "AlbaGroup", "version": "0.0.1", "private": true, "scripts": {"android": "npx expo run:android --device --variant devDebug", "android:pro": "APP_ENV=production && npx expo run:android --device --variant productionDebug --app-id com.alba.group.step.up", "apk": "cd android && ./gradlew app:clean app:assembleDevRelease && cd .. && open android/app/build/outputs/apk/dev/", "apk:pro": "cd android && ./gradlew app:clean app:assembleProductionRelease && cd .. && open android/app/build/outputs/apk/production/", "aab": "cd android && ./gradlew app:clean app:bundleDevRelease && cd .. && open android/app/build/outputs/bundle/devRelease/", "aab:pro": "APP_ENV=production cd android && ./gradlew app:clean app:bundleProductionRelease && cd .. && open android/app/build/outputs/bundle/productionRelease/", "ios": "npx expo run:ios --device", "ios:pro": "APP_ENV=production npx expo run:ios --device --scheme Product", "lint": "eslint .", "start": "npx expo start", "lint:fix": "eslint . --fix --ext .ts,.tsx,.js", "test": "jest", "clear:cache": "rm -rf node_modules && yarn cache clean && yarn && watchman watch-del-all && del %localappdata%Temphaste-map-* && del %localappdata%Tempmetro-cache && npx expo start --clear", "clear:expo": "npx expo start --clear", "env:push": "dotenv-vault push && dotenv-vault push production", "env:pull": "dotenv-vault pull && dotenv-vault pull production", "env:setup": "node scripts/setup-env.js", "adb:reactotron": "adb reverse tcp:9090 tcp:9090", "update-versions": "node scripts/update-readme-versions.js", "postinstall": "lefthook install && yarn update-versions", "doctor": "npx expo-doctor"}, "dependencies": {"@expo/react-native-action-sheet": "^4.1.1", "@gluestack-style/react": "^1.0.57", "@gluestack-ui/config": "^1.1.20", "@gluestack-ui/image": "^0.1.17", "@gluestack-ui/nativewind-utils": "1.0.26", "@gluestack-ui/themed": "^1.1.73", "@google-cloud/recaptcha-enterprise-react-native": "^18.7.1", "@maplibre/maplibre-react-native": "^10.1.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/slider": "^4.5.7", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/auth": "^22.2.0", "@react-native-vector-icons/ant-design": "^4.4.2", "@react-native-vector-icons/common": "^11.0.0", "@react-native-vector-icons/entypo": "^1.0.1", "@react-native-vector-icons/evil-icons": "^1.10.1", "@react-native-vector-icons/feather": "^4.29.2", "@react-native-vector-icons/ionicons": "^7.4.0", "@react-native-vector-icons/material-icons": "^0.0.1", "@react-navigation/bottom-tabs": "^7.3.12", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.2.10", "@shopify/flash-list": "1.7.6", "@tanstack/react-query": "^5.75.1", "axios": "1.9.0", "dotenv": "^16.5.0", "expo": "~53.0.0", "expo-intent-launcher": "^12.1.4", "expo-linear-gradient": "^14.1.4", "expo-linking": "^7.1.5", "expo-location": "^18.1.5", "formik": "^2.4.6", "moment": "^2.30.1", "nativewind": "^4.1.23", "radash": "^12.1.0", "react": "19.0.0", "react-native": "0.79.2", "react-native-date-picker": "^5.0.12", "react-native-gesture-handler": "~2.24.0", "react-native-otp-entry": "^1.8.4", "react-native-otp-input": "https://github.com/linhnguyen-gt/react-native-otp-input.git", "react-native-raw-bottom-sheet": "github:linhnguyen-gt/react-native-raw-bottom-sheet#1.0.1", "react-native-reanimated": "~3.17.4", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-vision-camera": "^4.6.4", "reactotron-react-native": "^5.1.13", "tailwindcss": "^3.4.17", "yup": "^1.6.1", "zod": "^3.24.3", "zustand": "^5.0.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.9.0", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "@types/react-dom": "^19", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "@typescript-eslint/eslint-plugin": "^7.4.0", "@typescript-eslint/parser": "^7.4.0", "babel-plugin-module-resolver": "^5.0.2", "dotenv-vault": "^1.26.2", "eslint": "^8.19.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-detox": "^1.0.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "jest": "^29.7.0", "lefthook": "^1.11.12", "lint-staged": "^15.5.1", "prettier": "^3.3.3", "react-dom": "19.0.0", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "engines": {"node": ">=18"}}