/**
 * Test script để verify iOS Map crash fix
 * Chạy script này để kiểm tra các thay đổi đã được áp dụng đúng
 */

const fs = require('fs');
const path = require('path');

function checkFileExists(filePath) {
    return fs.existsSync(filePath);
}

function readFileContent(filePath) {
    if (!checkFileExists(filePath)) {
        console.error(`❌ File không tồn tại: ${filePath}`);
        return null;
    }
    return fs.readFileSync(filePath, 'utf8');
}

function checkMapComponent() {
    console.log('\n🔍 Kiểm tra Map Component...');
    const filePath = 'src/presentation/components/map/Map.tsx';
    const content = readFileContent(filePath);
    
    if (!content) return false;
    
    const checks = [
        {
            name: 'isUnmountedRef được khai báo',
            test: content.includes('const isUnmountedRef = React.useRef<boolean>(false);')
        },
        {
            name: 'isMarkersLoading state được thêm',
            test: content.includes('const [isMarkersLoading, setIsMarkersLoading] = React.useState(false);')
        },
        {
            name: 'Stable key cho PointAnnotation',
            test: content.includes('const stableKey = `marker-${marker.id}-${marker.category || "default"}-${index}`;')
        },
        {
            name: 'Kiểm tra unmount trong callbacks',
            test: content.includes('if (isUnmountedRef.current) return;')
        },
        {
            name: 'Tăng delay cho iOS',
            test: content.includes('isIos ? 800 : 600') || content.includes('isIos ? 1000 : 600')
        }
    ];
    
    let passed = 0;
    checks.forEach(check => {
        if (check.test) {
            console.log(`✅ ${check.name}`);
            passed++;
        } else {
            console.log(`❌ ${check.name}`);
        }
    });
    
    return passed === checks.length;
}

function checkBinQueries() {
    console.log('\n🔍 Kiểm tra Bin Queries...');
    const filePath = 'src/data/queries/binQueries.ts';
    const content = readFileContent(filePath);
    
    if (!content) return false;
    
    const checks = [
        {
            name: 'iOS delays được tăng',
            test: content.includes('const IOS_FILTER_DELAY = 800;') && 
                  content.includes('const IOS_RESET_DELAY = 600;') &&
                  content.includes('const IOS_STATE_UPDATE_DELAY = 400;')
        },
        {
            name: 'isUnmountedRef được khai báo',
            test: content.includes('const isUnmountedRef = React.useRef<boolean>(false);')
        },
        {
            name: 'Kiểm tra unmount trong async operations',
            test: content.includes('if (activeRequestRef.current !== requestId || isUnmountedRef.current)')
        },
        {
            name: 'Cleanup với unmount flag',
            test: content.includes('isUnmountedRef.current = true;')
        }
    ];
    
    let passed = 0;
    checks.forEach(check => {
        if (check.test) {
            console.log(`✅ ${check.name}`);
            passed++;
        } else {
            console.log(`❌ ${check.name}`);
        }
    });
    
    return passed === checks.length;
}

function checkMapConstants() {
    console.log('\n🔍 Kiểm tra Map Constants...');
    const filePath = 'src/shared/helper/map.ts';
    const content = readFileContent(filePath);
    
    if (!content) return false;
    
    const checks = [
        {
            name: 'MAX_MARKERS_TO_RENDER giảm cho iOS',
            test: content.includes('export const MAX_MARKERS_TO_RENDER = isIos ? 30 : 100;')
        },
        {
            name: 'MARKER_BATCH_SIZE giảm cho iOS',
            test: content.includes('export const MARKER_BATCH_SIZE = isIos ? 5 : 20;')
        },
        {
            name: 'BATCH_DELAY tăng cho iOS',
            test: content.includes('export const BATCH_DELAY = isIos ? 200 : 100;')
        }
    ];
    
    let passed = 0;
    checks.forEach(check => {
        if (check.test) {
            console.log(`✅ ${check.name}`);
            passed++;
        } else {
            console.log(`❌ ${check.name}`);
        }
    });
    
    return passed === checks.length;
}

function runTests() {
    console.log('🚀 Bắt đầu kiểm tra iOS Map crash fix...\n');
    
    const results = [
        checkMapComponent(),
        checkBinQueries(),
        checkMapConstants()
    ];
    
    const allPassed = results.every(result => result);
    
    console.log('\n📊 Kết quả tổng quan:');
    if (allPassed) {
        console.log('✅ Tất cả các fix đã được áp dụng thành công!');
        console.log('🎉 iOS Map crash issue đã được khắc phục.');
        console.log('\n📝 Tiếp theo:');
        console.log('1. Test trên iOS device/simulator');
        console.log('2. Thử filter markers nhiều lần');
        console.log('3. Verify không có crash');
        console.log('4. Monitor performance');
    } else {
        console.log('❌ Một số fix chưa được áp dụng đúng.');
        console.log('🔧 Vui lòng kiểm tra lại các file đã sửa.');
    }
}

// Chạy tests
runTests();
