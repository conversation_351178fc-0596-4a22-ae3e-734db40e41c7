import { useMutation } from "@tanstack/react-query";
import React from "react";

import { useUserStore } from "@/app/store";

import { userApi } from "../api";

export const useUserQueries = () => {
    const { setCurrentUser, user } = useUserStore();

    const mutation = useMutation({
        mutationFn: userApi.getCurrentUser,
        onSuccess: (data) => {
            if (data?.ok) {
                setCurrentUser(data.data);
            }
        }
    });

    React.useEffect(() => {
        mutation.mutate();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const getUser = React.useCallback(async () => {
        try {
            const result = await mutation.mutateAsync();
            return result?.ok;
        } catch (error) {
            /* empty */
        }
    }, [mutation]);

    return {
        ...mutation,
        isLoading: mutation.isPending,
        user,
        getUser
    };
};

export const useUpdateUserQueries = () => {
    const { setCurrentUser } = useUserStore();

    const mutation = useMutation({
        mutationFn: userApi.updateUser
    });

    const updateUser = React.useCallback(
        async (user: UpdateUserRequest) => {
            try {
                const result = await mutation.mutateAsync({ user });
                if (result?.ok) {
                    setCurrentUser(result.data);
                }
            } catch (error) {
                /* empty */
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [mutation]
    );

    return {
        ...mutation,
        isLoading: mutation.isPending,
        updateUser
    };
};
