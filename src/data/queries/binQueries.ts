import { useNavigation } from "@react-navigation/native";
import { useMutation } from "@tanstack/react-query";
import React from "react";
import { Platform } from "react-native";

import { binApi } from "../api";

import { useBinStore } from "@/app/store/bins.store";
import { RouteName } from "@/shared/constants";
import { createDataFromDataObject } from "@/shared/helper";

// iOS-specific constants
const IOS_FILTER_DELAY = 500;
const IOS_RESET_DELAY = 400;
const IOS_STATE_UPDATE_DELAY = 300;

export const useBinQueries = () => {
    const { setBins, bins } = useBinStore();
    const [isRefreshing, setIsRefreshing] = React.useState(false);
    const refreshIntervalRef = React.useRef<ReturnType<typeof setInterval> | null>(null);

    const mutation = useMutation({
        mutationFn: binApi.getAll,
        onSuccess: (data) => {
            if (data?.ok) {
                setBins(data.data || []);
            }
        }
    });

    const getBins = React.useCallback(
        async (silent = false) => {
            if (silent) {
                setIsRefreshing(true);
            }

            try {
                const result = await mutation.mutateAsync();

                if (silent) {
                    setIsRefreshing(false);
                }

                if (result?.ok) {
                    setBins(result.data || []);
                }
            } catch (error) {
                if (silent) {
                    setIsRefreshing(false);
                }
            }
        },
        [mutation, setBins]
    );

    React.useEffect(() => {
        getBins();

        refreshIntervalRef.current = setInterval(() => {
            getBins(true);
        }, 300000);

        return () => {
            if (refreshIntervalRef.current) {
                clearInterval(refreshIntervalRef.current);
            }
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return {
        ...mutation,
        isLoading: mutation.isPending && !isRefreshing,
        isRefreshing,
        bins,
        refreshBins: () => getBins(true)
    };
};

export const useBinByWasteTypeIdQueries = () => {
    const { setBinsByWasteTypeId, binsByWasteTypeId } = useBinStore();
    const [isError, setIsError] = React.useState(false);
    const [isProcessing, setIsProcessing] = React.useState(false);
    const timeoutRef = React.useRef<ReturnType<typeof setTimeout> | null>(null);
    const activeRequestRef = React.useRef<number | null>(null);

    const mutation = useMutation({
        mutationFn: binApi.getByWasteTypeId,
        onError: () => {
            setIsError(true);
            setBinsByWasteTypeId(undefined);
        }
    });

    const getBinsByWasteTypeId = React.useCallback(
        async (wasteTypeId: number) => {
            try {
                // Clear any pending operations
                if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                    timeoutRef.current = null;
                }

                // Track the current request
                const requestId = Date.now();
                activeRequestRef.current = requestId;

                setIsError(false);
                setIsProcessing(true);

                // Special case for resetting filter
                if (wasteTypeId === 0) {
                    // For iOS, add a delay before clearing the filter
                    if (Platform.OS === "ios") {
                        await new Promise((resolve) => setTimeout(resolve, IOS_RESET_DELAY));

                        // Check if this request is still active
                        if (activeRequestRef.current !== requestId) {
                            return;
                        }
                    }

                    setBinsByWasteTypeId(undefined);
                    setIsProcessing(false);
                    return;
                }

                // For iOS, implement a debounce to prevent rapid filter changes
                if (Platform.OS === "ios") {
                    return new Promise<void>((resolve) => {
                        timeoutRef.current = setTimeout(async () => {
                            try {
                                // Check if this request is still active
                                if (activeRequestRef.current !== requestId) {
                                    setIsProcessing(false);
                                    resolve();
                                    return;
                                }

                                const result = await mutation.mutateAsync(wasteTypeId);

                                // Check again if this request is still active
                                if (activeRequestRef.current !== requestId) {
                                    setIsProcessing(false);
                                    resolve();
                                    return;
                                }

                                if (result?.ok && Array.isArray(result.data)) {
                                    // Add a small delay before updating the state on iOS
                                    await new Promise((r) => setTimeout(r, IOS_STATE_UPDATE_DELAY));

                                    // Final check if this request is still active
                                    if (activeRequestRef.current !== requestId) {
                                        setIsProcessing(false);
                                        resolve();
                                        return;
                                    }

                                    setBinsByWasteTypeId(result.data || []);
                                } else {
                                    // If API returns not ok, reset the filter
                                    setBinsByWasteTypeId(undefined);
                                }
                                setIsProcessing(false);
                                resolve();
                            } catch (error) {
                                console.error("Error fetching bins by waste type:", error);
                                setIsError(true);
                                setBinsByWasteTypeId(undefined);
                                setIsProcessing(false);
                                resolve();
                            }
                        }, IOS_FILTER_DELAY);
                    });
                } else {
                    // For Android, proceed normally
                    const result = await mutation.mutateAsync(wasteTypeId);

                    // Check if this request is still active
                    if (activeRequestRef.current !== requestId) {
                        setIsProcessing(false);
                        return;
                    }

                    if (result?.ok && Array.isArray(result.data)) {
                        setBinsByWasteTypeId(result.data || []);
                    } else {
                        // If API returns not ok, reset the filter
                        setBinsByWasteTypeId(undefined);
                    }
                    setIsProcessing(false);
                }
            } catch (error) {
                console.error("Error in getBinsByWasteTypeId:", error);
                setIsError(true);
                setBinsByWasteTypeId(undefined);
                setIsProcessing(false);
            }
        },
        [mutation, setBinsByWasteTypeId]
    );

    // Clean up timeouts when component unmounts
    React.useEffect(() => {
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    return {
        ...mutation,
        isLoading: (mutation.isPending && !isError) || isProcessing,
        binsByWasteTypeId,
        getBinsByWasteTypeId
    };
};

export const useBinQRDetailQueries = () => {
    const mutation = useMutation({
        mutationFn: binApi.getQRBinDetail
    });

    const getBinDetail = React.useCallback(
        async (url: string) => {
            try {
                const result = await mutation.mutateAsync(url);
                if (result?.ok) {
                    return result.data;
                }
            } catch (error) {
                /* empty */
            }
        },
        [mutation]
    );

    return {
        ...mutation,
        isLoading: mutation.isPending,
        getBinDetail
    };
};

export const useRecyclingQueries = () => {
    const navigation = useNavigation();

    const mutation = useMutation({
        mutationFn: binApi.recycling,
        onSuccess: (data) => {
            if (data?.ok) {
                navigation.navigate(RouteName.SubmittedRedeem, { result: data.data });
            }
        }
    });

    const recycling = React.useCallback(
        async (binId: number, photoPath: string) => {
            try {
                const uri = photoPath.startsWith("file://") ? photoPath : `file://${photoPath}`;

                const fileData = {
                    uri,
                    type: "image/jpeg",
                    name: "photo.jpg"
                };

                const formData = createDataFromDataObject({
                    bin_id: binId.toString(),
                    photo: fileData
                });

                const result = await mutation.mutateAsync(formData);
                if (result?.ok) {
                    return result.data;
                }
            } catch (error) {
                /* empty */
            }
        },
        [mutation]
    );

    return {
        ...mutation,
        isLoading: mutation.isPending,
        recycling
    };
};
