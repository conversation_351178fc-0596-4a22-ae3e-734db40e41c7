import { useNavigation } from "@react-navigation/native";
import { useMutation } from "@tanstack/react-query";
import React from "react";

import { binApi } from "../api";

import { useBinStore } from "@/app/store/bins.store";
import { RouteName } from "@/shared/constants";
import { createDataFromDataObject } from "@/shared/helper";

export const useBinQueries = () => {
    const { setBins, bins } = useBinStore();
    const [isRefreshing, setIsRefreshing] = React.useState(false);
    const refreshIntervalRef = React.useRef<ReturnType<typeof setInterval> | null>(null);

    const mutation = useMutation({
        mutationFn: binApi.getAll,
        onSuccess: (data) => {
            if (data?.ok) {
                setBins(data.data || []);
            }
        }
    });

    const getBins = React.useCallback(
        async (silent = false) => {
            if (silent) {
                setIsRefreshing(true);
            }

            const result = await mutation.mutateAsync();

            if (silent) {
                setIsRefreshing(false);
            }

            if (result?.ok) {
                setBins(result.data || []);
            }
        },
        [mutation, setBins]
    );

    React.useEffect(() => {
        getBins();

        refreshIntervalRef.current = setInterval(() => {
            getBins(true);
        }, 300000);

        return () => {
            if (refreshIntervalRef.current) {
                clearInterval(refreshIntervalRef.current);
            }
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return {
        ...mutation,
        isLoading: mutation.isPending && !isRefreshing,
        isRefreshing,
        bins,
        refreshBins: () => getBins(true)
    };
};

export const useBinByWasteTypeIdQueries = () => {
    const { setBinsByWasteTypeId, binsByWasteTypeId } = useBinStore();

    const mutation = useMutation({
        mutationFn: binApi.getByWasteTypeId
    });

    const getBinsByWasteTypeId = React.useCallback(
        async (wasteTypeId: number) => {
            const result = await mutation.mutateAsync(wasteTypeId);
            if (result?.ok) {
                setBinsByWasteTypeId(result.data || []);
            }
        },
        [mutation, setBinsByWasteTypeId]
    );

    return {
        ...mutation,
        isLoading: mutation.isPending,
        binsByWasteTypeId,
        getBinsByWasteTypeId
    };
};

export const useBinQRDetailQueries = () => {
    const mutation = useMutation({
        mutationFn: binApi.getQRBinDetail
    });

    const getBinDetail = React.useCallback(
        async (url: string) => {
            try {
                const result = await mutation.mutateAsync(url);
                if (result?.ok) {
                    return result.data;
                }
            } catch (error) {
                /* empty */
            }
        },
        [mutation]
    );

    return {
        ...mutation,
        isLoading: mutation.isPending,
        getBinDetail
    };
};

export const useRecyclingQueries = () => {
    const navigation = useNavigation();

    const mutation = useMutation({
        mutationFn: binApi.recycling,
        onSuccess: (data) => {
            if (data?.ok) {
                navigation.navigate(RouteName.SubmittedRedeem, { result: data.data });
            }
        }
    });

    const recycling = React.useCallback(
        async (binId: number, photoPath: string) => {
            try {
                const uri = photoPath.startsWith("file://") ? photoPath : `file://${photoPath}`;

                const fileData = {
                    uri,
                    type: "image/jpeg",
                    name: "photo.jpg"
                };

                const formData = createDataFromDataObject({
                    bin_id: binId.toString(),
                    photo: fileData
                });

                const result = await mutation.mutateAsync(formData);
                if (result?.ok) {
                    return result.data;
                }
            } catch (error) {
                /* empty */
            }
        },
        [mutation]
    );

    return {
        ...mutation,
        isLoading: mutation.isPending,
        recycling
    };
};
