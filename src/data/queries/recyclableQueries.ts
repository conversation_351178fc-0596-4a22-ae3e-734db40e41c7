import { useMutation } from "@tanstack/react-query";
import React from "react";

import { recyclablesApi } from "../api";

export const useRecyclablesQueries = () => {
    const [recyclables, setRecyclables] = React.useState<RecyclablesResponse[] | undefined>(undefined);

    const mutation = useMutation({
        mutationFn: recyclablesApi.getAll,
        onSuccess: (data) => {
            if (data?.ok) {
                setRecyclables(data.data);
            }
        }
    });

    React.useEffect(() => {
        mutation.mutate();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return {
        ...mutation,
        isLoading: mutation.isPending,
        recyclables
    };
};
