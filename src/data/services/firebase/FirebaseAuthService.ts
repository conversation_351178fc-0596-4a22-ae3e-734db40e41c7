import { getApp } from "@react-native-firebase/app";
import { FirebaseAuthTypes, getAuth, signInWithPhoneNumber, signOut } from "@react-native-firebase/auth";
import { Alert } from "react-native";

import { formatPhoneWithCountryCode } from "@/shared/helper";

export type OTPVerificationError = {
    code: "invalid-verification-code" | "expired-verification-code" | "too-many-requests" | "unknown";
    message: string;
};

class FirebaseAuthService {
    private static _instance: FirebaseAuthService;
    private confirmationResult: FirebaseAuthTypes.ConfirmationResult | null = null;
    private auth: ReturnType<typeof getAuth>;

    public static getInstance(): FirebaseAuthService {
        if (!FirebaseAuthService._instance) {
            FirebaseAuthService._instance = new FirebaseAuthService();
        }
        return FirebaseAuthService._instance;
    }

    private constructor() {
        this.auth = getAuth(getApp());
    }

    private showErrorAlert(message: string): void {
        Alert.alert("Error", message);
    }

    async sendOTP(phoneNumber: string | undefined | null): Promise<boolean> {
        try {
            const formattedPhone = formatPhoneWithCountryCode({ phone: phoneNumber });

            this.confirmationResult = await this.auth.signInWithPhoneNumber(formattedPhone);

            return true;
        } catch (error) {
            console.error("Phone authentication failed:", error);
            this.showErrorAlert(this.extractErrorMessage(error) || "An error occurred. Please try again later");
            return false;
        }
    }

    async verifyOTP(otp: string): Promise<boolean> {
        if (!this.confirmationResult) {
            this.showErrorAlert("Verification session expired. Please request a new OTP");
            return false;
        }

        if (!otp || otp.length !== 6) {
            this.showErrorAlert("OTP must be 6 digits");
            return false;
        }

        try {
            const userCredential = await this.confirmationResult.confirm(otp);
            if (!userCredential) {
                this.showErrorAlert("Verification failed. Please try again");
                return false;
            }
            return true;
        } catch (confirmError) {
            console.error("Phone verification failed:", confirmError);
            this.showErrorAlert(this.extractErrorMessage(confirmError) || "An error occurred. Please try again later");
            return false;
        }
    }

    getConfirmationResult(): FirebaseAuthTypes.ConfirmationResult | null {
        return this.confirmationResult;
    }

    async logout(): Promise<void> {
        if (!this.auth.currentUser) return;
        try {
            await signOut(this.auth);
        } catch (error) {
            this.showErrorAlert(this.extractErrorMessage(error) || "An error occurred. Please try again later");

            throw error;
        }
    }

    async checkIfPhoneRegistered({
        phoneNumber,
        errorText = "Phone number already registered"
    }: {
        phoneNumber: string;
        errorText?: string;
    }): Promise<boolean> {
        try {
            const formattedPhone = formatPhoneWithCountryCode({ phone: phoneNumber });

            const confirmationResult = await signInWithPhoneNumber(this.auth, formattedPhone);

            this.confirmationResult = confirmationResult;
            this.showErrorAlert(errorText);
            return true;
        } catch (error) {
            this.showErrorAlert(this.extractErrorMessage(error) || "An error occurred. Please try again later");
            return true;
        }
    }

    private extractErrorMessage(error: any): string {
        if (!error) return "";

        if (typeof error === "string") {
            const stringMatch = error.match(/\[\w+\/[\w-]+\]\s*(.+)/i);
            if (stringMatch && stringMatch[1]) {
                return stringMatch[1].trim();
            }
            return error;
        }

        if (error.message) {
            const bracketMatch = error.message.match(/\[\w+\/[\w-]+\]\s*(.+)/i);
            if (bracketMatch && bracketMatch[1]) {
                return bracketMatch[1].trim();
            }

            const colonMatch = error.message.match(/(?:\w+Error: \[\w+\/[\w-]+\]\s*)(.+)/i);
            if (colonMatch && colonMatch[1]) {
                return colonMatch[1].trim();
            }

            return error.message;
        }

        return JSON.stringify(error);
    }
}

export default FirebaseAuthService.getInstance();
