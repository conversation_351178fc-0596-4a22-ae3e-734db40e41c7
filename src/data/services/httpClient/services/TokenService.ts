import { resetAllStores } from "@/app/store";

import { RootNavigator } from "../../navigation";
import ApiMethod from "../ApiMethod";
import { HttpClient } from "../HttpClient";
import { ITokenService, Session } from "../interfaces/IHttpClient";

import { EndPoint, RouteName } from "@/shared/constants";
import { clearToken, getToken, setToken } from "@/shared/helper";

export class TokenService implements ITokenService {
    private readonly maxTimeout = 2 * 60 * 60 * 1000;
    private readonly refreshBuffer = 30 * 1000;
    private tokenRefreshTimer: ReturnType<typeof setTimeout> | null = null;
    private readonly httpClient: HttpClient;

    constructor(httpClient: HttpClient) {
        this.httpClient = httpClient;
    }

    async setSession(session: Session): Promise<void> {
        this.httpClient.setAccessToken(session.accessToken);

        let expiresAt: Date | undefined | null = session.accessTokenExpiresAt;

        if (expiresAt && !(expiresAt instanceof Date)) {
            try {
                expiresAt = new Date(expiresAt);
            } catch (error) {
                console.error("Invalid date format for accessTokenExpiresAt", error);
                expiresAt = null;
            }
        }

        await setToken({
            refreshToken: session.refreshToken,
            accessTokenExpiresAt: expiresAt
        });

        if (!expiresAt) return;

        this.scheduleTokenRefresh(expiresAt);
    }

    private scheduleTokenRefresh(expirationDate: Date): void {
        if (this.tokenRefreshTimer) {
            clearTimeout(this.tokenRefreshTimer);
            this.tokenRefreshTimer = null;
        }

        const now = new Date();
        const expirationTime = new Date(expirationDate).getTime();
        const timeUntilExpiration = expirationTime - now.getTime();

        if (timeUntilExpiration <= this.refreshBuffer) {
            this.refreshToken();
            return;
        }

        const timeoutDuration = Math.min(timeUntilExpiration - this.refreshBuffer, this.maxTimeout);

        this.tokenRefreshTimer = setTimeout(() => {
            const currentTime = new Date().getTime();
            const remainingTime = expirationTime - currentTime - this.refreshBuffer;

            if (remainingTime <= 0) {
                this.refreshToken();
            } else if (remainingTime <= this.maxTimeout) {
                this.tokenRefreshTimer = setTimeout(() => this.refreshToken(), remainingTime);
            } else {
                this.scheduleTokenRefresh(expirationDate);
            }
        }, timeoutDuration);
    }

    async clearSession(): Promise<void> {
        if (this.tokenRefreshTimer) {
            clearTimeout(this.tokenRefreshTimer);
            this.tokenRefreshTimer = null;
        }

        await setToken({
            refreshToken: null,
            accessTokenExpiresAt: null
        });
        this.httpClient.clearSession();
    }

    async getRefreshToken(): Promise<string | null> {
        const token = await getToken();
        return token ?? null;
    }

    async refreshToken(): Promise<boolean> {
        try {
            const refreshToken = await this.getRefreshToken();
            if (!refreshToken) return false;

            const response = await this.httpClient.request<{
                data: LoginResponse;
            }>({
                endpoint: EndPoint.auth.refreshToken,
                method: ApiMethod.POST,
                body: {
                    refresh_token: refreshToken
                }
            });

            if (!response?.ok) {
                await clearToken();
                this.httpClient.clearSession();
                resetAllStores();
                RootNavigator.replaceName(RouteName.Welcome);
                return false;
            }

            const data = response.data?.data;
            const accessTokenExpiresAt = data?.access_token_expires_at
                ? new Date(data.access_token_expires_at)
                : undefined;
            const refreshTokenExpiresAt = data?.refresh_token_expires_at
                ? new Date(data.refresh_token_expires_at)
                : undefined;

            await this.setSession({
                accessToken: data?.access_token,
                refreshToken: data?.refresh_token,
                accessTokenExpiresAt,
                refreshTokenExpiresAt
            });
            return true;
        } catch (e) {
            console.error("Error refreshing token:", e);
            await this.clearSession();
            return false;
        }
    }

    async logout(): Promise<void> {
        const logoutResponse = await this.httpClient.request<void>({
            endpoint: EndPoint.auth.logout,
            method: ApiMethod.GET
        });

        if (!logoutResponse?.ok) return;
        await clearToken();
        this.httpClient.clearSession();
        resetAllStores();
        RootNavigator.replaceName(RouteName.Welcome);
    }
}
