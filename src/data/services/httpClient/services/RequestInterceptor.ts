import { AxiosError, AxiosInstance, HttpStatusCode } from "axios";

import { resetAllStores } from "@/app/store";

import { RootNavigator } from "../../navigation";

import { TokenService } from "./TokenService";

import { RouteName } from "@/shared/constants";
import { clearToken } from "@/shared/helper";

interface ErrorResponseData {
    message: string;
    status?: number;
}

export class RequestInterceptor {
    constructor(
        private readonly axiosInstance: AxiosInstance,
        private readonly tokenService: TokenService
    ) {}

    setupInterceptors(): void {
        this.axiosInstance.interceptors.request.use(this.handleRequest.bind(this), this.handleRequestError.bind(this));

        this.axiosInstance.interceptors.response.use(this.handleResponse.bind(this), async (error: AxiosError) => {
            if (this.isTokenExpiredError(error)) {
                try {
                    await clearToken();
                    this.tokenService.clearSession();
                    resetAllStores();
                    RootNavigator.replaceName(RouteName.Welcome);
                } catch (refreshError) {
                    return Promise.reject({
                        message: "Session expired, please login again",
                        status: HttpStatusCode.Unauthorized,
                        code: "TOKEN_EXPIRED"
                    });
                }
            }

            if (error.response?.data) {
                const errorData = error.response.data as ErrorResponseData;
                return Promise.reject({
                    ...error,
                    message: errorData.message,
                    status: error.response.status
                });
            }

            return Promise.reject(error);
        });
    }

    private async handleRequest(config: any) {
        // TODO: Add request handling logic (logging, metrics, etc.)
        return config;
    }

    private handleRequestError(error: AxiosError) {
        return Promise.reject(error);
    }

    private handleResponse(response: any) {
        return response;
    }

    private isTokenExpiredError(error: AxiosError): boolean {
        return error.response?.status === HttpStatusCode.Unauthorized;
    }
}
