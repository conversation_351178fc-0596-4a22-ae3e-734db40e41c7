export interface INavigationService {
    navigate<RouteName extends keyof RootStackParamList, <PERSON><PERSON> extends RootStackParamList[RouteName]>(
        route: RouteName,
        params?: Param
    ): Promise<void>;

    goBack(): void;

    replaceName<RouteName extends keyof RootStackParamList, <PERSON><PERSON> extends RootStackParamList[RouteName]>(
        route: RouteName,
        params?: Param
    ): Promise<void>;
}
