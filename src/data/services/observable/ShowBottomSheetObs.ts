import { Subject, catchError, filter, of, share } from "rxjs";

class ShowBottomSheetObs {
    private static subject$ = new Subject<ShowBottomSheetObsPayload>();
    private static readonly stream$ = ShowBottomSheetObs.subject$.pipe(
        filter((payload): payload is ShowBottomSheetObsPayload => {
            if (!payload) return false;
            return true;
        }),
        catchError((error) => {
            console.error("ShowBottomSheetObs stream error:", error);
            return of(null);
        }),
        share()
    );

    static subscribe(callback: (payload: ShowBottomSheetObsPayload) => void) {
        return ShowBottomSheetObs.stream$
            .pipe(filter((payload): payload is ShowBottomSheetObsPayload => payload !== null))
            .subscribe({
                next: callback,
                error: (error) => {
                    console.error("ShowBottomSheetObs subscription error:", error);
                }
            });
    }

    static action(params?: Omit<ShowBottomSheetObsPayload, "value">): void {
        try {
            ShowBottomSheetObs.subject$.next(params || {});
        } catch (error) {
            console.error("ShowBottomSheetObs action error:", error);
        }
    }
}

export default ShowBottomSheetObs;

declare global {
    export type ShowBottomSheetObsPayload = {
        title?: string;
        message?: string;
        titleButtonConfirm?: string;
        titleButtonCancel?: string;
        closeOnDragDown?: boolean;
        closeOnPressMask?: boolean;
        onConfirm?: () => void;
        onCancel?: () => void;
        height?: number;
    };
}
