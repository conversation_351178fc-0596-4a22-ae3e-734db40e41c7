import { Subject, catchError, filter, map, of, share } from "rxjs";

class DateTimeObs {
    private static subject$ = new Subject<DateTimeObsPayload>();
    private static readonly stream$ = DateTimeObs.subject$.pipe(
        filter((payload): payload is DateTimeObsPayload => {
            if (!payload) return false;
            const isValidDate = payload.value instanceof Date && !isNaN(payload.value.getTime());
            const isValidMode = ["date", "time", "datetime"].includes(payload.mode as string);

            if (!isValidDate) {
                return false;
            }
            if (!isValidMode) {
                return false;
            }
            return true;
        }),
        map((payload) => ({
            ...payload,
            value: new Date(payload.value)
        })),
        catchError((error) => {
            console.error("DateTimeObs stream error:", error);
            return of(null);
        }),
        share()
    );

    static subscribe(callback: (payload: DateTimeObsPayload) => void) {
        return DateTimeObs.stream$
            .pipe(filter((payload): payload is DateTimeObsPayload => payload !== null))
            .subscribe({
                next: callback,
                error: (error) => {
                    console.error("DateTimeObs subscription error:", error);
                }
            });
    }

    static action(params: Omit<DateTimeObsPayload, "value"> & { value?: Date | string | number }): void {
        try {
            const value = params.value ? new Date(params.value) : new Date();
            DateTimeObs.subject$.next({
                ...params,
                value,
                onConfirm: params.onConfirm ?? (() => {}),
                onCancel: params.onCancel
            });
        } catch (error) {
            console.error("DateTimeObs action error:", error);
        }
    }
}

export default DateTimeObs;

declare global {
    export type DateTimeObsPayload = {
        mode: "date" | "time" | "datetime";
        value: Date;
        onConfirm: (data: Date) => void;
        onCancel?: () => void;
    };
}
