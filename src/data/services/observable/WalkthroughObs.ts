import { Subject } from "rxjs";

class WalkthroughService {
    private walkthroughSubject = new Subject<{
        show: boolean;
    }>();

    public walkthrough$ = this.walkthroughSubject.asObservable();

    public showWalkthrough(show: boolean): void {
        this.walkthroughSubject.next({ show });
    }

    public hideWalkthrough(): void {
        this.walkthroughSubject.next({ show: false });
    }
}

export const walkthroughService = new WalkthroughService();
