import { Api<PERSON>eth<PERSON>, HttpClient } from "../services/httpClient";

import { EndPoint } from "@/shared/constants";
import { createDataFromDataObject, trimObjectStrings } from "@/shared/helper";

export const authApi = {
    registerIndividual: async (
        registerRequest: RegisterRequest["individual"]
    ): Promise<BaseResponse<RegisterResponse>> => {
        const request = trimObjectStrings({
            first_name: registerRequest?.firstName,
            last_name: registerRequest?.lastName,
            email: registerRequest?.email,
            phone: registerRequest?.phone,
            password: registerRequest?.password,
            password_confirmation: registerRequest?.password,
            dob: registerRequest?.dob,
            address: registerRequest?.address,
            postal_code: registerRequest?.postalCode
        });

        const response = await HttpClient.request<{
            data: RegisterResponse;
        }>({
            endpoint: EndPoint.auth.registerIndividual,
            method: ApiMethod.POST,
            headers: {
                "Content-Type": "multipart/form-data"
            },
            body: createDataFromDataObject(request)
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    },

    registerEntity: async (registerRequest: RegisterRequest["school"]): Promise<BaseResponse<RegisterResponse>> => {
        const request = trimObjectStrings({
            name: registerRequest?.name,
            email: registerRequest?.email,
            phone: registerRequest?.phone,
            address: registerRequest?.address,
            postal_code: registerRequest?.postalCode,
            password: registerRequest?.password,
            password_confirmation: registerRequest?.password
        });

        const response = await HttpClient.request<{
            data: RegisterResponse;
        }>({
            endpoint: EndPoint.auth.registerEntity.replace("{entity}", registerRequest?.type || ""),
            method: ApiMethod.POST,
            headers: {
                "Content-Type": "multipart/form-data"
            },
            body: createDataFromDataObject(request)
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    },

    login: async ({
        login,
        password,
        isOrganization
    }: {
        login: string;
        password: string;
        isOrganization: boolean;
    }): Promise<BaseResponse<LoginResponse>> => {
        const body = {
            type: isOrganization ? "school" : "user",
            key: login,
            password
        };
        const response = await HttpClient.request<{
            data: LoginResponse;
        }>({
            endpoint: EndPoint.auth.login,
            method: ApiMethod.POST,
            body: trimObjectStrings(body)
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    }
};
