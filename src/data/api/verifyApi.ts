import { ApiMethod, HttpClient } from "../services/httpClient";

import { EndPoint } from "@/shared/constants";

export const verifyApi = {
    verify: async ({ phone, otp }: { phone: string; otp: string }): Promise<BaseResponse<void>> => {
        const response = await HttpClient.request({
            endpoint: EndPoint.verify.verify,
            method: ApiMethod.POST,
            body: {
                phone,
                verify_code: otp
            }
        });

        if (!response?.ok) return;

        return { ok: response.ok };
    },

    sendCode: async (verify: VerifyRequest): Promise<BaseResponse<{ code: string }>> => {
        const response = await HttpClient.request<{
            data: {
                code: string;
            };
        }>({
            endpoint: EndPoint.verify.resendCode,
            method: ApiMethod.POST,
            body: {
                phone: verify.phone,
                action: verify.action
            }
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    }
};
