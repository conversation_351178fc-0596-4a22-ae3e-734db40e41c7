import { ApiMethod, HttpClient } from "../services/httpClient";

import { EndPoint } from "@/shared/constants";

export const binApi = {
    getAll: async (): Promise<BaseResponse<BinResponse[]>> => {
        const response = await HttpClient.request<{
            data: {
                locations: BinResponse[];
            };
        }>({
            endpoint: EndPoint.bin.getAll,
            method: ApiMethod.POST
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data.locations };
    },

    getByWasteTypeId: async (wasteTypeId: number): Promise<BaseResponse<BinResponse[]>> => {
        const response = await HttpClient.request<{
            data: {
                locations: BinResponse[];
            };
        }>({
            endpoint: EndPoint.bin.getByWasteTypeId + wasteTypeId,
            method: ApiMethod.POST
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data.locations };
    },

    getQRBinDetail: async (url: string): Promise<BaseResponse<BinDetailResponse>> => {
        const response = await HttpClient.request<{
            data: BinDetailResponse;
        }>({
            endpoint: url,
            method: ApiMethod.GET
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    },

    recycling: async (formData: FormData): Promise<BaseResponse<RecyclingResponse>> => {
        const response = await HttpClient.request<{
            data: RecyclingResponse;
        }>({
            endpoint: EndPoint.bin.recycling,
            method: ApiMethod.POST,
            headers: {
                "Content-Type": "multipart/form-data"
            },
            body: formData
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    }
};
