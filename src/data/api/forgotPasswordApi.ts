import { ApiMethod, HttpClient } from "../services/httpClient";

import { EndPoint } from "@/shared/constants";
import { trimObjectStrings } from "@/shared/helper";

export const forgotPasswordApi = {
    resetPassword: async (forgotPasswordRequest: ForgotPasswordRequest): Promise<BaseResponse<void>> => {
        const response = await HttpClient.request<{
            data: void;
        }>({
            endpoint: EndPoint.auth.resetPassword,
            method: ApiMethod.POST,
            body: trimObjectStrings(forgotPasswordRequest)
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    }
};
