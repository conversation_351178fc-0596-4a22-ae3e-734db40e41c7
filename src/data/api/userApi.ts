import { ApiMethod, HttpClient } from "../services/httpClient";

import { EndPoint } from "@/shared/constants";
import { trimObjectStrings } from "@/shared/helper";

export const userApi = {
    getCurrentUser: async (): Promise<BaseResponse<User>> => {
        const response = await HttpClient.request<{
            data: User;
        }>({
            endpoint: EndPoint.user.currentUser,
            method: ApiMethod.GET
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    },

    updateUser: async ({ user }: { user: UpdateUserRequest }): Promise<BaseResponse<User>> => {
        const request = trimObjectStrings({
            first_name: user?.first_name,
            last_name: user?.last_name,
            dob: user?.dob,
            address: user?.address,
            postal_code: user?.postal_code
        });

        const response = await HttpClient.request<{
            data: { user: User };
        }>({
            endpoint: EndPoint.user.currentUserUpdate,
            method: ApiMethod.POST,
            body: request
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data.user };
    }
};
