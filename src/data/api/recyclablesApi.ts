import { ApiMethod, HttpClient } from "../services/httpClient";

import { EndPoint } from "@/shared/constants";

export const recyclablesApi = {
    getAll: async (): Promise<BaseResponse<RecyclablesResponse[]>> => {
        const response = await HttpClient.request<{
            data: { waste_types: RecyclablesResponse[] };
        }>({
            endpoint: EndPoint.acceptedRecyclables.getAll,
            method: ApiMethod.POST
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data.waste_types };
    }
};
