import { createStore } from "./store.factory";

type BinState = BinStateData & {
    setBins: (bins: BinResponse[]) => void;
    setBinsByWasteTypeId: (bins: BinResponse[] | undefined) => void;
    clearBins: () => void;
    reset: () => void;
};

const initialState: BinStateData = {
    bins: undefined,
    binsByWasteTypeId: undefined
};

export const useBinStore = createStore<BinState>("Bin", (set) => ({
    ...initialState,
    setBins: (bins) => set({ bins }),
    setBinsByWasteTypeId: (bins) => set({ binsByWasteTypeId: bins }),
    clearBins: () => set({ bins: undefined }),
    reset: () => set(initialState)
}));
