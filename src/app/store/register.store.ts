import { createStore } from "./store.factory";

type RegisterState = RegisterStateData & {
    setIndividual: (individual?: RegisterRequest["individual"]) => void;
    setSchool: (school?: RegisterRequest["school"]) => void;
    clearRegister: () => void;
    reset: () => void;
};

const initialState: RegisterStateData = {
    request: undefined
};

export const useRegisterStore = createStore<RegisterState>("Register", (set) => ({
    ...initialState,
    setIndividual: (individual) =>
        set((state) => ({ request: individual ? { ...state.request, individual } : state.request })),
    setSchool: (school) => set((state) => ({ request: school ? { ...state.request, school } : state.request })),
    clearRegister: () => set({ request: undefined }),
    reset: () => set(initialState)
}));
