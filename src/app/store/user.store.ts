import { createStore } from "./store.factory";

type UserState = UserStateData & {
    setCurrentUser: (user?: User) => void;
    clearUser: () => void;
    reset: () => void;
};

const initialState: UserStateData = {
    user: undefined
};

export const useUserStore = createStore<UserState>("User", (set) => ({
    ...initialState,
    setCurrentUser: (user) => set({ user }),
    clearUser: () => set({ user: undefined }),
    reset: () => set(initialState)
}));
