const Errors = {
    REQUIRED_EMAIL_INPUT: "Please enter your email",
    REQUIRED_USERNAME_INPUT: "Please enter your username",
    REQUIRED_PASSWORD_INPUT: "Please enter your password",
    EMAIL_INVALID: "Invalid email address",
    IS_NOT_EMAIL: "Email must end with .com",
    REQUIRED_FULLNAME_INPUT: "Please enter your full name",
    REQUIRED_CONFIRM_PASSWORD_INPUT: "Please confirm your password",
    PASSWORD_NOT_MATCH: "Passwords do not match",
    PASSWORD_MIN_LENGTH: "Password must be at least 6 characters",
    REQUIRED_PHONE_NUMBER_INPUT: "Please enter your phone number",
    REQUIRED_FIRST_NAME_INPUT: "Please enter your first name",
    REQUIRED_LAST_NAME_INPUT: "Please enter your last name",
    REQUIRED_DOB_INPUT: "Please enter your date of birth",
    RE<PERSON><PERSON><PERSON><PERSON>_ADDRESS_INPUT: "Please enter your address",
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_POSTAL_CODE_INPUT: "Please enter your postal code",
    RE<PERSON><PERSON>RED_SCHOOL_NAME_INPUT: "Please enter your school name",
    REQUIRED_OTP_INPUT: "Please enter your OTP",
    INVALID_OTP_LENGTH: "OTP must be {length} characters long",
    INVALID_OTP_FORMAT: "OTP must be a number",
    USERNAME_INVALID: "Username must be at least 3 characters and contain only letters, numbers, and underscores",

    DATE_FUTURE: "Date of birth cannot be in the future",
    TOO_OLD: "You must be at least 18 years old",
    MINIMUM_AGE: "You must be at least 18 years old",
    REQUIRED_BUSINESS_TYPE_INPUT: "Please select your business type"
} as const;

export default Errors;
