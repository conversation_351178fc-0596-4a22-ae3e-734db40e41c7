import ImageAssets from "./ImageAssets";

export const E_WASTE_BIN_TYPES = {
    ICT_BATTERY_BULB: 1, // TODO: ICT, Battery, Bulb Bin (3 in 1 Bin)
    BATTERIES_BULBS: 2, // TODO: Batteries & Bulbs Bin
    BATTERIES: 3 // TODO: Batteries Bin
} as const;

export const BIN_TYPES = {
    REVERSE_VENDING_MACHINE: 2, // TODO: Reverse Vending Machine
    RECYCLING_BIN: 3 // TODO: Recycling Bin
} as const;

export type BinTypeKey = "ictBulbBattery" | "bulbsBatteries" | "batteries" | "reverseVending" | "recycling";

export const BIN_TYPE_MAPPING = {
    [`e_waste_bin_type_id_${E_WASTE_BIN_TYPES.ICT_BATTERY_BULB}`]: "ictBulbBattery",
    [`e_waste_bin_type_id_${E_WASTE_BIN_TYPES.BATTERIES_BULBS}`]: "bulbsBatteries",
    [`e_waste_bin_type_id_${E_WASTE_BIN_TYPES.BATTERIES}`]: "batteries",

    [`bin_type_id_${BIN_TYPES.REVERSE_VENDING_MACHINE}`]: "reverseVending",
    [`bin_type_id_${BIN_TYPES.RECYCLING_BIN}`]: "recycling"
} as const;

export const BIN_CATEGORY_MAPPING = {
    [`e_waste_bin_type_id_${E_WASTE_BIN_TYPES.ICT_BATTERY_BULB}`]: "ICT Equipments",
    [`e_waste_bin_type_id_${E_WASTE_BIN_TYPES.BATTERIES_BULBS}`]: "Batteries & Lamps",
    [`e_waste_bin_type_id_${E_WASTE_BIN_TYPES.BATTERIES}`]: "Batteries",

    [`bin_type_id_${BIN_TYPES.REVERSE_VENDING_MACHINE}`]: "Recycling",
    [`bin_type_id_${BIN_TYPES.RECYCLING_BIN}`]: "Recycling"
} as const;

export function determineBinType(binTypeId?: number, eWasteBinTypeId?: number | null): string {
    if (eWasteBinTypeId) {
        const eWasteKey = `e_waste_bin_type_id_${eWasteBinTypeId}` as keyof typeof BIN_TYPE_MAPPING;
        if (eWasteKey in BIN_TYPE_MAPPING) {
            return BIN_TYPE_MAPPING[eWasteKey];
        }
    }

    if (binTypeId) {
        const binKey = `bin_type_id_${binTypeId}` as keyof typeof BIN_TYPE_MAPPING;
        if (binKey in BIN_TYPE_MAPPING) {
            return BIN_TYPE_MAPPING[binKey];
        }
    }

    return "ictBulbBattery";
}

export function determineBinCategory(binTypeId?: number, eWasteBinTypeId?: number | null): string {
    if (eWasteBinTypeId) {
        const eWasteKey = `e_waste_bin_type_id_${eWasteBinTypeId}` as keyof typeof BIN_CATEGORY_MAPPING;
        if (eWasteKey in BIN_CATEGORY_MAPPING) {
            return BIN_CATEGORY_MAPPING[eWasteKey];
        }
    }

    if (binTypeId) {
        const binKey = `bin_type_id_${binTypeId}` as keyof typeof BIN_CATEGORY_MAPPING;
        if (binKey in BIN_CATEGORY_MAPPING) {
            return BIN_CATEGORY_MAPPING[binKey];
        }
    }

    return "Other";
}

const binInfo = {
    ictBulbBattery: {
        name: "ICT, Bulb, Battery Bin",
        acceptedItems: [
            { name: "Printer", icon: ImageAssets.printer },
            { name: "Computer & Laptop", icon: ImageAssets.laptop },
            { name: "Mobile & Tablet", icon: ImageAssets.smartphone },
            { name: "Network & Set-top Box", icon: ImageAssets.hardDrive },
            { name: "TV & Desktop Monitor", icon: ImageAssets.tvMinimal },
            { name: "Bulb & Battery", icon: ImageAssets.batteryFull }
        ]
    },
    bulbsBatteries: {
        name: "Bulbs & Batteries Bin",
        acceptedItems: [
            { name: "Household Battery", icon: ImageAssets.batteryFull },
            { name: "Bulb", icon: ImageAssets.lightbulb }
        ]
    },
    batteries: {
        name: "Batteries Bin",
        acceptedItems: [
            { name: "PP3 Battery", icon: ImageAssets.batteryFull },
            { name: "AA Battery", icon: ImageAssets.batteryFull },
            { name: "AAA Battery", icon: ImageAssets.batteryFull },
            { name: "1,5V Battery", icon: ImageAssets.batteryFull }
        ]
    },
    reverseVending: {
        name: "Reverse Vending Machine",
        acceptedItems: [
            { name: "Empty Water Bottle", icon: ImageAssets.glassWater },
            { name: "Empty Aluminium Can", icon: ImageAssets.glassWater }
        ]
    },
    recycling: {
        name: "Recycling Bin",
        acceptedItems: [
            { name: "Paper", icon: ImageAssets.newspaper },
            { name: "Metal", icon: ImageAssets.hardDrive },
            { name: "Plastic", icon: ImageAssets.pillBottle },
            { name: "Glass", icon: ImageAssets.glassWater }
        ]
    }
};

export const getBinImageForType = (binType: BinTypeKey) => {
    switch (binType) {
        case "bulbsBatteries":
            return ImageAssets.bulbEwasteBins;
        case "batteries":
            return ImageAssets.batteriesEwasteBins;
        case "reverseVending":
            return ImageAssets.rvm;
        case "recycling":
            return ImageAssets.recycleBin;
        default:
            return ImageAssets.ictBulbEwasteBins;
    }
};

const BinTypes = {
    E_WASTE_BIN_TYPES,
    BIN_TYPES,
    BIN_TYPE_MAPPING,
    BIN_CATEGORY_MAPPING,
    determineBinType,
    determineBinCategory,
    binInfo,
    getBinImageForType
} as const;

export default BinTypes;
