const EndPoint = {
    auth: {
        login: "auth/Login",
        registerIndividual: "auth/Register",
        logout: "auth/Logout",
        refreshToken: "auth/RefreshToken",
        resetPassword: "auth/ResetPassword",
        registerEntity: "auth/RegisterEntity/{entity}"
    },
    verify: {
        verify: "auth/Verify",
        resendCode: "auth/ResendCode"
    },
    user: {
        currentUser: "currentUser",
        currentUserUpdate: "currentUser/Update"
    },
    bin: {
        getAll: "bin-management/getAll",
        getByWasteTypeId: "bin-management/getAll?waste_type_id=",
        recycling: "recyclings/submit"
    },
    acceptedRecyclables: {
        getAll: "accepted-recyclables/getAll"
    }
} as const;

export default EndPoint;
