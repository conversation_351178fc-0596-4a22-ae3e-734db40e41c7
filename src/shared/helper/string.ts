/**
 * Safely trims a string value, handling null and undefined values
 * @param value The string value to trim
 * @returns Trimmed string or undefined if input is null/undefined
 */
export const safeTrim = (value?: string | null): string | undefined => {
    return value?.trim();
};

/**
 * Recursively trims all string properties in an object
 * @param obj The object with string properties to trim
 * @returns A new object with all string properties trimmed
 */
export const trimObjectStrings = <T extends Record<string, any>>(obj: T): T => {
    if (!obj || typeof obj !== "object") return obj;

    const result: Record<string, any> = {};

    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const value = obj[key];

            if (typeof value === "string") {
                result[key] = value.trim();
            } else if (value && typeof value === "object" && !Array.isArray(value)) {
                result[key] = trimObjectStrings(value);
            } else {
                result[key] = value;
            }
        }
    }

    return result as T;
};
