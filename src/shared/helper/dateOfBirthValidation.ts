import { Alert } from "react-native";

import { Errors } from "../constants";

type DateValidationResult = {
    isValid: boolean;
    errorMessage?: string;
};

export const validateDateOfBirth = (dateString: string): DateValidationResult => {
    const date = new Date(dateString);
    const today = new Date();

    if (date > today) {
        return { isValid: false, errorMessage: Errors.DATE_FUTURE };
    }

    const maxAge = new Date();
    maxAge.setFullYear(today.getFullYear() - 120);
    if (date < maxAge) {
        return { isValid: false, errorMessage: Errors.TOO_OLD };
    }

    const minAge = new Date();
    minAge.setFullYear(today.getFullYear() - 18);
    if (date > minAge) {
        return { isValid: false, errorMessage: Errors.MINIMUM_AGE };
    }

    return { isValid: true };
};

export const validateDateWithAlert = (dateString: string): boolean => {
    const validation = validateDateOfBirth(dateString);

    if (!validation.isValid && validation.errorMessage) {
        Alert.alert(validation.errorMessage);
        return false;
    }

    return true;
};
