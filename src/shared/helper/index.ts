import { isEqual } from "radash";
import { Dimensions } from "react-native";

export * from "./dateFormat";
export * from "./dateOfBirthValidation";
export * from "./device";
export * from "./formDataHelper";
export { default as Logger } from "./logger";
export * from "./navigation";
export * from "./phoneNumber";
export * from "./storage";
export * from "./string";
export * from "./map";

export const fullWidth = Dimensions.get("window").width;

export const fullHeight = Dimensions.get("window").height;
/**
 * Checks if two values are deeply equal
 * @param val1 First value to compare
 * @param val2 Second value to compare
 * @returns True if values are equal, false otherwise
 * @example
 * compareValue({ a: 1 }, { a: 1 }) // returns true
 * compareValue([1, 2], [1, 2]) // returns true
 */
export const compareValue = <T>(val1: T, val2: T): boolean => {
    return isEqual(val1, val2);
};
