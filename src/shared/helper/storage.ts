import AsyncStorage from "@react-native-async-storage/async-storage";

enum TypeToken {
    RefreshToken = "REFRESH_TOKEN",
    AccessTokenExpiresAt = "ACCESS_TOKEN_EXPIRES_AT"
}

enum StorageKeys {
    FirstLaunch = "FIRST_LAUNCH"
}

/**
 * Saves tokens and expiration data to AsyncStorage
 * @param param0 Object containing optional refreshToken and accessTokenExpiresAt
 * @example
 * await setToken({
 *   refreshToken: 'new-refresh-token',
 *   accessTokenExpiresAt: new Date('2023-01-01')
 * })
 */
export const setToken = async ({
    refreshToken,
    accessTokenExpiresAt
}: {
    refreshToken?: string | undefined | null;
    accessTokenExpiresAt?: Date | undefined | null;
}) => {
    const promises: Promise<void>[] = [];

    if (refreshToken !== undefined) {
        if (refreshToken === null) {
            promises.push(AsyncStorage.removeItem(TypeToken.RefreshToken));
        } else {
            promises.push(AsyncStorage.setItem(TypeToken.RefreshToken, refreshToken));
        }
    }

    if (accessTokenExpiresAt !== undefined) {
        if (accessTokenExpiresAt === null) {
            promises.push(AsyncStorage.removeItem(TypeToken.AccessTokenExpiresAt));
        } else if (accessTokenExpiresAt instanceof Date) {
            promises.push(AsyncStorage.setItem(TypeToken.AccessTokenExpiresAt, accessTokenExpiresAt.toISOString()));
        }
    }

    if (promises.length > 0) {
        await Promise.all(promises);
    }
};

/**
 * Retrieves a token from AsyncStorage
 * @returns Promise resolving to the refresh token string or undefined
 * @example
 * const token = await getToken()
 */
export const getToken = async () => (await AsyncStorage.getItem(TypeToken.RefreshToken)) ?? undefined;

/**
 * Retrieves the access token expiration timestamp
 * @returns Promise resolving to the expiration Date or undefined
 * @example
 * const expiresAt = await getAccessTokenExpiration()
 */
export const getAccessTokenExpiration = async (): Promise<Date | undefined> => {
    const expiresAtStr = await AsyncStorage.getItem(TypeToken.AccessTokenExpiresAt);
    return expiresAtStr ? new Date(expiresAtStr) : undefined;
};

/**
 * Clears all tokens including refresh token and expiration data
 * @returns Promise that resolves when clearing is complete
 * @example
 * await clearToken()
 */
export const clearToken = async () => {
    await Promise.all([
        AsyncStorage.removeItem(TypeToken.RefreshToken),
        AsyncStorage.removeItem(TypeToken.AccessTokenExpiresAt)
    ]);
};

/**
 * Checks if this is the first time the app has been launched
 * @returns Promise resolving to true if this is the first launch, false otherwise
 * @example
 * const isFirstLaunch = await isFirstAppLaunch()
 */
export const isFirstAppLaunch = async (): Promise<boolean> => {
    try {
        const value = await AsyncStorage.getItem(StorageKeys.FirstLaunch);
        return value !== null;
    } catch (error) {
        console.error("Error checking first launch status:", error);
        return false;
    }
};

/**
 * Marks the app as having been launched
 * @returns Promise that resolves when setting is complete
 * @example
 * await markAppAsLaunched()
 */
export const markAppAsLaunched = async (): Promise<void> => {
    try {
        await AsyncStorage.setItem(StorageKeys.FirstLaunch, "false");
    } catch (error) {
        console.error("Error marking app as launched:", error);
    }
};

export const clearFirstAppLaunch = async (): Promise<void> => {
    try {
        await AsyncStorage.removeItem(StorageKeys.FirstLaunch);
    } catch (error) {
        console.error("Error clearing first launch status:", error);
    }
};
