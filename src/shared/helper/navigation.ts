import { CardStyleInterpolators, StackNavigationOptions } from "@react-navigation/stack";
import { Easing } from "react-native";

import { isIos } from "./device";

export const defaultOptions: StackNavigationOptions = {
    headerShown: false,
    cardStyle: {
        backgroundColor: "white"
    }
};

export const screenOptions = (): StackNavigationOptions => {
    return {
        ...defaultOptions,
        transitionSpec: {
            open: {
                animation: "timing",
                config: { duration: 200, easing: Easing.linear }
            },
            close: {
                animation: "timing",
                config: { duration: 200, easing: Easing.linear }
            }
        },
        cardStyleInterpolator: isIos
            ? CardStyleInterpolators.forHorizontalIOS
            : CardStyleInterpolators.forFadeFromCenter
    };
};
