import moment from "moment";

/**
 * Formats a date with special handling for today and yesterday
 * @param date Date string or Date object to format
 * @returns Formatted string with "Today" or "Yesterday" prefix if applicable
 * @example
 * formatDateWithToday(new Date()) // returns "Today - 15 March, 2024"
 * formatDateWithToday('2024-03-14') // returns "Yesterday - 14 March, 2024"
 * formatDateWithToday('2024-03-01') // returns "01 March, 2024"
 */
export const formatDateWithToday = (date: string | Date): string => {
    const momentDate = moment(date);
    const today = moment().startOf("day");

    if (momentDate.isSame(today, "day")) {
        return `Today - ${momentDate.format("DD MMMM, YYYY")}`;
    }

    if (momentDate.isSame(today.clone().subtract(1, "day"), "day")) {
        return `Yesterday - ${momentDate.format("DD MMMM, YYYY")}`;
    }

    return momentDate.format("DD MMMM, YYYY");
};

/**
 * Formats a date range, combining dates if they're the same day
 * @param startDate Start date string or Date object
 * @param endDate End date string or Date object
 * @returns Formatted date range string
 * @example
 * formatDateRange('2024-03-15', '2024-03-15')
 * // returns "Today - 15 March, 2024"
 *
 * formatDateRange('2024-03-01', '2024-03-15')
 * // returns "01 March, 2024 - Today - 15 March, 2024"
 */
export const formatDateRange = (startDate: string | Date, endDate: string | Date): string => {
    const start = moment(startDate);
    const end = moment(endDate);

    if (start.isSame(end, "day")) {
        return formatDateWithToday(startDate);
    }

    return `${formatDateWithToday(startDate)} - ${formatDateWithToday(endDate)}`;
};

/**
 * Formats a date string or Date object to specified format
 * @param date Date to format
 * @param format Moment.js format string (default: "DD MMMM YYYY")
 * @returns Formatted date string or empty string if date is undefined
 * @example
 * formatDate('2024-03-15') // returns "15 March 2024"
 * formatDate('2024-03-15', 'YYYY/MM/DD') // returns "2024/03/15"
 * formatDate() // returns ""
 */
export const formatDate = (date?: string | Date, format: string = "DD/MM/YYYY"): string => {
    return date ? moment(date).format(format) : "";
};

/**
 * Formats a date string or Date object to 24-hour time format
 * @param date Date to format
 * @returns Time string in HH:mm format
 * @example
 * formatTime('2024-03-15T14:30:00') // returns "14:30"
 * formatTime(new Date('2024-03-15T09:05:00')) // returns "09:05"
 */
export const formatTime = (date: string | Date): string => {
    return moment(date).format("HH:mm");
};

/**
 * Combines a new date with existing time from a datetime
 * @param dateText - New date string
 * @param existingDateTime - Optional existing datetime to preserve time from
 * @returns Combined datetime as ISO string
 */
export const combineDateWithTime = (dateText: string, existingDateTime?: string): string => {
    const newDate = new Date(dateText);
    const finalDate = newDate;

    if (existingDateTime) {
        const oldDateTime = new Date(existingDateTime);
        finalDate.setHours(oldDateTime.getHours());
        finalDate.setMinutes(oldDateTime.getMinutes());
        finalDate.setSeconds(oldDateTime.getSeconds());
    }

    return finalDate.toISOString();
};

/**
 * Combines a new time with existing date from a datetime
 * @param timeText - New time string
 * @param existingDateTime - Optional existing datetime to preserve date from
 * @returns Combined datetime as ISO string
 */
export const combineTimeWithDate = (timeText: string, existingDateTime?: string): string => {
    const newTime = new Date(timeText);
    let finalDate: Date;

    if (existingDateTime) {
        finalDate = new Date(existingDateTime);
        finalDate.setHours(newTime.getHours());
        finalDate.setMinutes(newTime.getMinutes());
        finalDate.setSeconds(newTime.getSeconds());
    } else {
        finalDate = newTime;
    }

    return finalDate.toISOString();
};

/**
 * Formats a date relative to current time with specific display rules:
 * - Today: "today | HH:mm A"
 * - Within 7 days: "X days ago"
 * - Older than 7 days: "DD MMM, YYYY"
 * @param date Date string or Date object to format
 * @returns Formatted date string
 * @example
 * formatRelativeDate('2024-03-15T09:24:00Z') // returns "today | 09:24 AM" (if today)
 * formatRelativeDate('2024-03-13T09:24:00Z') // returns "2 days ago"
 * formatRelativeDate('2024-03-01T09:24:00Z') // returns "01 Mar, 2024"
 */
export const formatRelativeDate = (date: string | Date): string => {
    const momentDate = moment.utc(date).local();
    const now = moment();

    const diffDays = now.clone().startOf("day").diff(momentDate.clone().startOf("day"), "days");

    // Today
    if (diffDays === 0) {
        return `today | ${momentDate.format("hh:mm A")}`;
    }

    // Within last 7 days
    if (diffDays > 0 && diffDays <= 7) {
        return `${diffDays} ${diffDays === 1 ? "day" : "days"} ago`;
    }

    // Older than 7 days
    return momentDate.format("DD MMM, YYYY");
};
