import { isIos } from "./device";

export const MAX_MARKERS_TO_RENDER = isIos ? 50 : 100;
export const MARKER_BATCH_SIZE = isIos ? 10 : 20;
export const BATCH_DELAY = 100;

export const createGeoJSONCircle = (
    center: [number, number],
    radiusInKm: number,
    points: number = 64
): GeoJSON.Feature => {
    if (radiusInKm <= 0) {
        return {
            type: "Feature",
            geometry: {
                type: "Polygon",
                coordinates: [
                    [
                        [center[0], center[1]],
                        [center[0] + 0.0001, center[1]],
                        [center[0] + 0.0001, center[1] + 0.0001],
                        [center[0], center[1]]
                    ]
                ]
            },
            properties: {}
        };
    }

    const coords = {
        latitude: center[1],
        longitude: center[0]
    };

    const km = radiusInKm;

    const ret: number[][] = [];

    const distanceX = km / (111.32 * Math.cos((coords.latitude * Math.PI) / 180));

    const distanceY = km / 110.574;

    let theta, x, y;
    for (let i = 0; i < points; i++) {
        theta = (i / points) * (2 * Math.PI);
        x = distanceX * Math.cos(theta);
        y = distanceY * Math.sin(theta);

        ret.push([coords.longitude + x, coords.latitude + y]);
    }
    ret.push(ret[0]);

    return {
        type: "Feature",
        geometry: {
            type: "Polygon",
            coordinates: [ret]
        },
        properties: {}
    };
};
