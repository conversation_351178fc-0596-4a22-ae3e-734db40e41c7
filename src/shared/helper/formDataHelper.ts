/**
 * Creates a FormData object from a plain JavaScript object
 * @template Data Type of the input object (must be a Record with string/number values)
 * @param paramObject Object to convert to FormData
 * @returns FormData object with all key-value pairs from the input object
 * @example
 * const data = {
 *   name: '<PERSON>',
 *   age: 30,
 *   photo: fileObject
 * };
 *
 * const formData = createDataFromDataObject(data);
 * // Returns FormData with:
 * // - name: 'John'
 * // - age: '30'
 * // - photo: fileObject
 *
 * // Use with fetch:
 * fetch(url, {
 *   method: 'POST',
 *   body: createDataFromDataObject(data)
 * });
 */
export const createDataFromDataObject = <Data extends Record<string, any | number>>(paramObject?: Data): FormData => {
    if (!paramObject) return new FormData();
    const form_data = new FormData();
    for (const key in paramObject) {
        form_data.append(key, paramObject[key]);
    }
    return form_data;
};

/**
 * Finds a name in a dropdown data array by its value
 * @param data Array of dropdown items with name and value properties
 * @param value The value to search for
 * @returns The name of the matching item or empty string if not found
 */
export const getDropdownNameByValue = (
    data: Array<{ name: string; value: number }>,
    value?: string | number
): string => {
    if (!value) return "--";
    const numericValue = typeof value === "string" ? Number(value) : value;
    return data.find((item) => item.value === numericValue)?.name || "--";
};
