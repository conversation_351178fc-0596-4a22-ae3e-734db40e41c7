/**
 * Splits a phone number string into country code and number
 * @param phoneNumber - Full phone number (e.g., "+65 12345678")
 * @returns Object containing countryCode and number
 */
export const splitPhoneNumber = (phoneNumber?: string | false) => {
    if (!phoneNumber) {
        return {
            countryCode: "",
            number: ""
        };
    }

    const [countryCodePart, ...numberParts] = phoneNumber.split(" ");
    return {
        countryCode: countryCodePart.replace("+", ""),
        number: numberParts.join(" ")
    };
};

/**
 * Combines country code and number into a formatted phone number
 * @param countryCode - Country code without + (e.g., "65")
 * @param number - Phone number
 * @returns Formatted phone number (e.g., "+65 12345678")
 */
export const formatPhoneNumber = ({
    countryCode = "65",
    phoneNumber
}: {
    countryCode?: string;
    phoneNumber?: string;
}) => {
    if (!countryCode.trim() || !phoneNumber?.trim()) return "";
    return `+${countryCode.trim()} ${phoneNumber.trim()}`;
};

/**
 * Formats a phone number with country code and removes leading zero after country code
 * Example: "+84 0353940866" becomes "+84 353940866"
 *
 * @param phone The phone number to format
 * @returns Formatted phone number
 */
export const formatPhoneWithCountryCode = ({
    phone,
    noSpace = true
}: {
    phone?: string | null;
    noSpace?: boolean;
}): string => {
    if (!phone) return "";

    const formattedPhone = phone.trim();

    if (formattedPhone.startsWith("+")) {
        const countryCodeMatch = formattedPhone.match(/\+(\d+)/);

        if (countryCodeMatch && countryCodeMatch[1]) {
            const countryCode = "+" + countryCodeMatch[1];
            const restOfPhone = formattedPhone.substring(countryCode.length).trim();
            const phoneNumber = restOfPhone.startsWith("0") ? restOfPhone.substring(1) : restOfPhone;

            return noSpace ? `${countryCode}${phoneNumber}` : `${countryCode} ${phoneNumber}`;
        }
    }

    return formattedPhone;
};
