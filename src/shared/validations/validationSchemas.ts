import { BehaviorSubject } from "rxjs";
import { ref, string } from "yup";

import Errors from "../constants/Errors";

/**
 * Validation schemas for common form fields
 * These can be reused across different forms in the application
 */

// RxJS subject to track postal code validation state
export const postalCodeValidatingSubject = new BehaviorSubject<boolean>(false);

/**
 * Email validation schema
 * - Must be a valid email format
 * - Is required
 */
export const emailSchema = string().email(Errors.EMAIL_INVALID).required(Errors.REQUIRED_EMAIL_INPUT);

/**
 * Username validation schema
 * - Must be at least 3 characters
 * - Can only contain letters, numbers, and underscores
 * - Is required
 */
export const usernameSchema = string()
    .required(Errors.REQUIRED_USERNAME_INPUT)
    .matches(/^[a-zA-Z0-9_]{3,}$/, Errors.USERNAME_INVALID);

/**
 * Login validation schema
 * - Can be either a valid email or a username
 * - Is required
 */
export const loginSchema = string()
    .required((ctx) => {
        const isUsernameField = ctx.path?.toLowerCase().includes("username");
        return isUsernameField ? Errors.REQUIRED_USERNAME_INPUT : Errors.REQUIRED_EMAIL_INPUT;
    })
    .test("is-email-or-username", (value, ctx) => {
        if (!value) return true;

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const isEmail = emailRegex.test(value);

        const usernameRegex = /^[a-zA-Z0-9_]{3,}$/;
        const isUsername = usernameRegex.test(value);

        if (!isEmail && !isUsername) {
            // Check if the field is being used for email or username based on context
            const isUsernameField = ctx.path?.toLowerCase().includes("username");
            return ctx.createError({
                message: isUsernameField ? Errors.USERNAME_INVALID : Errors.EMAIL_INVALID
            });
        }

        return true;
    });

/**
 * Password validation schema
 * - Is required
 */
export const passwordSchema = string().required(Errors.REQUIRED_PASSWORD_INPUT);

/**
 * Strong password validation schema
 * - Is required
 * - Minimum 6 characters
 */
export const strongPasswordSchema = string()
    .required(Errors.REQUIRED_PASSWORD_INPUT)
    .min(6, Errors.PASSWORD_MIN_LENGTH);

/**
 * Confirm password validation schema
 * - Is required
 * - Must match password field
 * @param passwordField - The name of the password field to match against
 */
export const confirmPasswordSchema = (passwordField: string) =>
    string()
        .oneOf([ref(passwordField), undefined], Errors.PASSWORD_NOT_MATCH)
        .required(Errors.REQUIRED_CONFIRM_PASSWORD_INPUT);

/**
 * Required string validation schema
 * - Is required with custom error message
 * @param errorMessage - Custom error message for required field
 */
export const requiredStringSchema = (errorMessage: string) => string().required(errorMessage);

/**
 * Phone number validation schema
 * - Is required
 */
export const phoneNumberSchema = string().required(Errors.REQUIRED_PHONE_NUMBER_INPUT);

/**
 * OTP validation schema
 * - Is required
 * - Must be 6 characters long
 * - Must be a number
 */
export const otpSchema = string()
    .required(Errors.REQUIRED_OTP_INPUT)
    .length(6, Errors.INVALID_OTP_LENGTH.replace("{length}", "6"))
    .matches(/^\d+$/, Errors.INVALID_OTP_FORMAT);

/**
 * First name validation schema
 * - Is required
 */
export const firstNameSchema = string().required(Errors.REQUIRED_FIRST_NAME_INPUT);

/**
 * Last name validation schema
 * - Is required
 */
export const lastNameSchema = string().required(Errors.REQUIRED_LAST_NAME_INPUT);

/**
 * Address validation schema
 * - Is required
 */
export const addressSchema = string().required(Errors.REQUIRED_ADDRESS_INPUT);

/**
 * Postal code validation schema
 * - Is required
 * - Must be 6 digits
 * - Validates against OneMap API to check if postal code exists
 */
export const postalCodeSchema = string()
    .required(Errors.REQUIRED_POSTAL_CODE_INPUT)
    .matches(/^\d{6}$/, "Postal code needs to be 6 digits")
    .test("is-valid-postal-code", "Invalid postal code", async (value) => {
        if (!value || value.length !== 6) return true;

        try {
            postalCodeValidatingSubject.next(true);
            const response = await fetch(
                `https://www.onemap.gov.sg/api/common/elastic/search?searchVal=${value}&returnGeom=Y&getAddrDetails=Y&pageNum=1`
            );
            const data = await response.json();

            return data.results && data.results.length > 0;
        } catch (error) {
            postalCodeValidatingSubject.next(false);
            return true;
        } finally {
            postalCodeValidatingSubject.next(false);
        }
    });

/**
 * Date of birth validation schema
 * - Is required
 */
export const dobSchema = string().required(Errors.REQUIRED_DOB_INPUT);

/**
 * School name validation schema
 * - Is required
 */
export const schoolNameSchema = string().required(Errors.REQUIRED_SCHOOL_NAME_INPUT);

/**
 * Business type validation schema
 * - Is required
 */
export const businessTypeSchema = string().required(Errors.REQUIRED_BUSINESS_TYPE_INPUT);
