import { NavigationProp as BaseNavi<PERSON><PERSON>rop, ParamListBase, RouteProp, Theme } from "@react-navigation/native";

export interface NavigationProp<T extends ParamListBase = RootStackParamList> extends BaseNavigationProp<T> {
    navigate<RouteName extends keyof RootStackParamList, <PERSON><PERSON> extends RootStackParamList[RouteName]>(
        route: RouteName,
        params?: Param
    ): Promise<void>;
    goBack(): void;
    replace<RouteName extends keyof RootStackParamList, <PERSON><PERSON> extends RootStackParamList[RouteName]>(
        route: RouteName,
        params?: Param
    ): Promise<void>;
}

declare module "@react-navigation/native" {
    export function useNavigation<T extends NavigationProp = NavigationProp<RootStackParamList>>(): T;

    export function useRoute<T extends RouteProp<RootStackParamList, keyof RootStackParamList>>(): T;

    export const NavigationContainer: React.ComponentType<{
        children: React.ReactNode;
        theme?: Theme;
        [key: string]: any;
    }>;

    export type NavigationContainerRef<T extends ParamListBase = RootStackParamList> = {
        current?: {
            dispatch: (action: NavigationAction) => void;
            navigate: <RouteName extends keyof T>(name: RouteName, params?: T[RouteName]) => void;
            goBack: () => void;
            replace: <RouteName extends keyof T>(name: RouteName, params?: T[RouteName]) => void;
            reset: (state: {
                index: number;
                routes: Array<{
                    name: keyof T;
                    params?: T[keyof T];
                }>;
            }) => void;
        };
        isReady: () => boolean;
    };

    export function createNavigationContainerRef<
        T extends ParamListBase = RootStackParamList
    >(): NavigationContainerRef<T>;

    export type NavigationAction = {
        type: string;
        payload?: {
            name?: string;
            params?: object;
            index?: number;
            routes?: Array<{
                name: string;
                params?: object;
            }>;
        };
    };
}
