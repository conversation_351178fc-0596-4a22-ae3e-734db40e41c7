import { z } from "zod";

import { BinTypes } from "../constants";

type BinType = (typeof BinTypes.BIN_TYPES)[keyof typeof BinTypes.BIN_TYPES];
type EWasteBinType = (typeof BinTypes.E_WASTE_BIN_TYPES)[keyof typeof BinTypes.E_WASTE_BIN_TYPES];

export const BinTypeSchema = z.object({
    id: z.number(),
    name: z.string(),
    image: z.null(),
    fixed_qrcode: z.number(),
    point: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
    qrcode_type: z.string(),
    color: z.string(),
    image_url: z.string().nullable(),
    icon_url: z.string().nullable(),
    waste_types: z.array(
        z.object({
            id: z.number(),
            name: z.string(),
            created_at: z.string(),
            updated_at: z.string(),
            pivot: z.object({ bin_type_id: z.number(), waste_type_id: z.number() })
        })
    )
});

export const BinResponseSchema = z.object({
    id: z.number(),
    bin_type_id: z.number().refine((val): val is BinType => Object.values(BinTypes.BIN_TYPES).includes(val as BinType)),
    address: z.string(),
    lat: z.string(),
    long: z.string(),
    map_radius: z.string(),
    status: z.boolean(),
    created_at: z.string(),
    updated_at: z.string(),
    e_waste_bin_type_id: z
        .number()
        .refine((val): val is EWasteBinType => Object.values(BinTypes.E_WASTE_BIN_TYPES).includes(val as EWasteBinType))
        .nullable(),
    qrcode: z.null(),
    remark: z.null(),
    status_text: z.string(),
    google_maps_url: z.string(),
    apple_maps_url: z.string(),
    code: z.string(),
    type: BinTypeSchema
});

export const BinDetailResponseSchema = z.object({
    id: z.number(),
    bin_type_id: z.number().refine((val): val is BinType => Object.values(BinTypes.BIN_TYPES).includes(val as BinType)),
    address: z.string(),
    lat: z.string(),
    long: z.string(),
    map_radius: z.string(),
    status: z.boolean(),
    created_at: z.string(),
    updated_at: z.string(),
    e_waste_bin_type_id: z
        .number()
        .refine((val): val is EWasteBinType => Object.values(BinTypes.E_WASTE_BIN_TYPES).includes(val as EWasteBinType))
        .nullable(),
    qrcode: z.null(),
    remark: z.null(),
    status_text: z.string(),
    google_maps_url: z.string(),
    apple_maps_url: z.string(),
    code: z.string(),
    type: BinTypeSchema
});

export const RecyclingResponseSchema = z.object({
    additional_point: z.string(),
    total_point: z.number()
});

declare global {
    type BinResponse = z.infer<typeof BinResponseSchema>;

    type BinDetailResponse = z.infer<typeof BinDetailResponseSchema>;

    type RecyclingResponse = z.infer<typeof RecyclingResponseSchema>;

    export type BinStateData = {
        bins: BinResponse[] | undefined;
        binsByWasteTypeId: BinResponse[] | undefined;
    };
}
