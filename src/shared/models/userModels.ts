import * as z from "zod";

export const UserSchema = z.object({
    id: z.number(),
    name: z.string(),
    first_name: z.string(),
    last_name: z.string(),
    username: z.null(),
    email: z.string(),
    phone: z.string(),
    dob: z.null(),
    address: z.null(),
    postal_code: z.null(),
    email_verified_at: z.string(),
    status: z.boolean(),
    profile_photo_path: z.null(),
    verified: z.number(),
    verify_code: z.null(),
    verification_expires_at: z.null(),
    last_login: z.null(),
    point: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
    role_name: z.string(),
    profile_photo_url: z.string(),
    status_text: z.string(),
    unique_id: z.null(),
    roles: z.array(
        z.object({
            id: z.number(),
            name: z.string(),
            guard_name: z.string(),
            created_at: z.string(),
            updated_at: z.string(),
            pivot: z.object({
                model_type: z.string(),
                model_id: z.number(),
                role_id: z.number()
            })
        })
    )
});

export const UpdateUserSchema = z.object({
    first_name: z.string().optional(),
    last_name: z.string().optional(),
    address: z.string().optional(),
    dob: z.string().optional(),
    postal_code: z.string().optional()
});

declare global {
    type User = z.infer<typeof UserSchema>;
    type UpdateUserRequest = z.infer<typeof UpdateUserSchema>;

    type UserStateData = {
        user?: User;
    };
}
