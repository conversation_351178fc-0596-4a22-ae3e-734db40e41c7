import * as z from "zod";

import BusinessType from "../constants/BusinessType";

import { UserSchema } from "./userModels";

export const RegisterRequestSchema = z.object({
    individual: z
        .object({
            firstName: z.string().optional(),
            lastName: z.string().optional(),
            dob: z.string().optional(),
            address: z.string().optional(),
            postalCode: z.string().optional(),
            email: z.string().optional(),
            password: z.string().optional(),
            phone: z.string().optional(),
            countryCode: z.string().optional()
        })
        .optional(),
    school: z
        .object({
            name: z.string().optional(),
            address: z.string().optional(),
            postalCode: z.string().optional(),
            email: z.string().optional(),
            phone: z.string().optional(),
            type: z.nativeEnum(BusinessType).optional(),
            password: z.string().optional()
        })
        .optional()
});

export const RegisterResponseSchema = z.object({
    user: UserSchema.optional(),
    code: z.string().optional()
});

declare global {
    type RegisterResponse = z.infer<typeof RegisterResponseSchema>;
    type RegisterRequest = z.infer<typeof RegisterRequestSchema>;

    export type RegisterStateData = {
        request: RegisterRequest | undefined;
    };
}
