import * as z from "zod";

import { UserSchema } from "./userModels";

export const LoginResponseSchema = z.object({
    access_token: z.string().optional(),
    access_token_expires_at: z.coerce.date().optional(),
    refresh_token: z.string().optional(),
    refresh_token_expires_at: z.coerce.date().optional(),
    user: UserSchema.optional()
});

declare global {
    type LoginResponse = z.infer<typeof LoginResponseSchema>;

    export type LoginResponseData = {
        login: LoginResponse;
    };
}
