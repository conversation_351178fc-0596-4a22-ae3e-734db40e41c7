import { BottomTabBarProps, BottomTabNavigationOptions, createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import React from "react";

import { useUserQueries } from "@/data/queries";

import { BottomTab } from "../components/tab";
import { Box } from "../components/ui";
import { ActivityScreen } from "../screens/activity";
import { EventsScreen } from "../screens/events";
import { HomeScreen } from "../screens/home";
import { RewardsScreen } from "../screens/rewards";

import { RouteName } from "@/shared/constants";

const Tabs = createBottomTabNavigator();

const bottomTabOptions: BottomTabNavigationOptions = {
    tabBarHideOnKeyboard: true,
    headerShown: false
};

const BoxTab = () => <Box />;

const BottomNavigator = () => {
    useUserQueries();

    const renderTabBar = React.useCallback((props: BottomTabBarProps) => <BottomTab {...props} />, []);

    const tabs = React.useMemo(() => {
        const defaultTabs = [
            { key: "home", name: RouteName.Home, component: HomeScreen },
            { key: "rewards", name: RouteName.Rewards, component: RewardsScreen },
            { key: "scan", name: "Box", component: BoxTab },
            { key: "event", name: RouteName.Event, component: EventsScreen },
            { key: "activity", name: RouteName.Activity, component: ActivityScreen }
        ];

        return defaultTabs;
    }, []);

    return (
        <Tabs.Navigator tabBar={renderTabBar} screenOptions={bottomTabOptions} initialRouteName={RouteName.Home}>
            {tabs.map((tab) => (
                <Tabs.Screen key={tab.key} name={tab.name} component={tab.component} />
            ))}
        </Tabs.Navigator>
    );
};

export default BottomNavigator;
