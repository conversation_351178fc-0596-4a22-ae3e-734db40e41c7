import { createStackNavigator } from "@react-navigation/stack";
import React from "react";

import { ConfirmPhotoScreen } from "../screens/confirmPhoto";
import { SubmittedRedeemScreen } from "../screens/redeemPoint";
import { ScanScreen } from "../screens/scan";
import { TookPhotoScreen } from "../screens/tookPhoto";

import { RouteName } from "@/shared/constants";
import { screenOptions } from "@/shared/helper";

const Stack = createStackNavigator();

const ScanStack = () => {
    return (
        <Stack.Navigator screenOptions={screenOptions} initialRouteName={RouteName.Scan}>
            <Stack.Screen name={RouteName.Scan} component={ScanScreen} />
            <Stack.Screen name={RouteName.TookPhoto} component={TookPhotoScreen} />
            <Stack.Screen name={RouteName.ConfirmPhoto} component={ConfirmPhotoScreen} />
            <Stack.Screen
                name={RouteName.SubmittedRedeem}
                component={SubmittedRedeemScreen}
                options={{ gestureEnabled: false }}
            />
        </Stack.Navigator>
    );
};

export default ScanStack;
