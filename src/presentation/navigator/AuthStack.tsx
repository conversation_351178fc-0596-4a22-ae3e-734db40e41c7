import { createStackNavigator } from "@react-navigation/stack";
import React from "react";

import {
    ForgotPasswordScreen,
    LoginScreen,
    RegisterScreen,
    SchoolsRegistration,
    SetNewPassword,
    VerificationScreen,
    WelcomeSchool
} from "../screens/auth";

import { RouteName } from "@/shared/constants";
import { screenOptions } from "@/shared/helper";

const Stack = createStackNavigator();

const AuthStack = () => {
    return (
        <Stack.Navigator screenOptions={screenOptions} initialRouteName={RouteName.Login}>
            <Stack.Screen name={RouteName.Login} component={LoginScreen} />
            <Stack.Screen name={RouteName.ForgotPassword} component={ForgotPasswordScreen} />
            <Stack.Screen name={RouteName.Verification} component={VerificationScreen} />
            <Stack.Screen name={RouteName.SetNewPassword} component={SetNewPassword} />
            <Stack.Screen name={RouteName.Register} component={RegisterScreen} />
            <Stack.Screen name={RouteName.WelcomeSchools} component={WelcomeSchool} />
            <Stack.Screen name={RouteName.SchoolsRegistration} component={SchoolsRegistration} />
        </Stack.Navigator>
    );
};

export default AuthStack;
