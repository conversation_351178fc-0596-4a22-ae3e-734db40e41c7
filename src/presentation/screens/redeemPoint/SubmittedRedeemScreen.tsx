import { useFocusEffect } from "@react-navigation/native";
import React from "react";
import { BackHandler, ImageBackground } from "react-native";

import { useRouteParams } from "@/presentation/hooks";

import { RootNavigator } from "@/data/services/navigation";
import { BoxCard } from "@/presentation/components/boxCard";
import { MyButton } from "@/presentation/components/myButton";
import { Box, Container, HStack, IconComponent, Image, Text } from "@/presentation/components/ui";
import { usePoint } from "@/presentation/hooks/point";
import { ImageAssets, RouteName } from "@/shared/constants";
import { isIos } from "@/shared/helper";

const SubmittedRedeemScreen: React.FC = () => {
    const params = useRouteParams<typeof RouteName.SubmittedRedeem>();
    const earnedPoints = params?.result?.total_point || 0;
    const { point } = usePoint();

    const handleClose = () => {
        RootNavigator.replaceName(RouteName.Bottom, { screen: RouteName.Home });
    };

    useFocusEffect(
        React.useCallback(() => {
            if (isIos) return;
            const onBackPress = () => {
                handleClose();
                return true;
            };

            const subscription = BackHandler.addEventListener("hardwareBackPress", onBackPress);

            return () => subscription.remove();
        }, [])
    );

    const renderPointsCard = React.useMemo(
        () => (
            <BoxCard>
                <Box className="p-5 gap-y-4">
                    <HStack className="items-center justify-between">
                        <Text className="text-base mb-1 text-white">
                            CO<Text className="text-xs align-top text-white">2</Text> Points
                        </Text>
                        <HStack className="items-center gap-x-2">
                            <Image source={ImageAssets.icPoint} className="w-[20px] h-[20px]" alt="Point" />
                            <Text className="text-2xl font-bold text-white">{point}</Text>
                        </HStack>
                    </HStack>
                    <Image source={ImageAssets.lineWhite} className="h-[2px] w-full" resizeMode="cover" alt="Line" />

                    <Box className="bg-green-100">
                        <Text className="text-center text-sm text-white">
                            1 CO<Text className="text-xs align-top ">2</Text> point equals to{" "}
                            {params?.result?.additional_point || 0}
                        </Text>
                    </Box>
                </Box>
            </BoxCard>
        ),
        [params?.result?.additional_point, point]
    );

    return (
        <Container>
            <ImageBackground source={ImageAssets.backgroundSubmitRedeem} className="flex-1">
                <Box className="flex-1 relative">
                    <Box className="absolute top-5 left-5">
                        <IconComponent name="close" font="ionicons" color="white" size={24} onPress={handleClose} />
                    </Box>

                    <Box className="flex-1 items-center justify-center px-6 gap-y-5">
                        <Image
                            source={ImageAssets.icPointBig}
                            className="w-[120px] h-[100px]"
                            resizeMode="contain"
                            alt="Point"
                        />

                        <Box className="flex-col items-center gap-y-1">
                            <Text className="text-white text-center text-xl mb-4">
                                Congratulations! You&apos;ve earned
                            </Text>

                            <Box className="flex-row items-center justify-center mb-4">
                                <Text className="text-white font-bold text-5xl">{earnedPoints}</Text>
                                <Box className="ml-2">
                                    <Text className="text-white text-lg">
                                        CO<Text className="text-sm align-text-top">2</Text> Points
                                    </Text>
                                </Box>
                            </Box>

                            <Text className="text-white text-center text-lg mb-12">
                                This means you save 1 kg of CO2!
                            </Text>
                        </Box>
                    </Box>

                    <Box className="bg-white rounded-t-3xl px-6 pt-8 pb-10">
                        <Text className="text-gray-800 text-lg font-semibold mb-4 font-bold">
                            Your total CO<Text className="text-xs align-text-top">2</Text> points
                        </Text>

                        {renderPointsCard}

                        <MyButton text="Redeem Points" height={56} titleSize={18} />
                    </Box>
                </Box>
            </ImageBackground>
        </Container>
    );
};

export default SubmittedRedeemScreen;
