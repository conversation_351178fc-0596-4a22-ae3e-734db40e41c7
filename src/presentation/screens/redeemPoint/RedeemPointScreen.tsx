import React from "react";

import { getColor } from "@/presentation/hooks";

import { ShowBottomSheetObs } from "@/data/services/observable";
import { AmountSection } from "@/presentation/components/amount";
import { Header } from "@/presentation/components/header";
import { MyButton } from "@/presentation/components/myButton";
import { RedeemToSection } from "@/presentation/components/redeem";
import { Box, Container, IconComponent, ScrollView, Text } from "@/presentation/components/ui";

const RedeemPointScreen: React.FC = () => {
    const [points] = React.useState("2,000");
    const [balance] = React.useState("8,934");
    const [rewardPoints] = React.useState("550");

    const handleRedeemPoints = React.useCallback(() => {
        ShowBottomSheetObs.action({
            title: "Yay! You got the voucher",
            message: "Check it out on your My Voucher page",
            titleButtonConfirm: "Okay"
        });
    }, []);

    return (
        <Container>
            <Header title="Redeem Points" />

            <ScrollView className="flex-1">
                <Box>
                    <Box className="gap-y-3">
                        <AmountSection points={points} balance={balance} />
                        <RedeemToSection rewardPoints={rewardPoints} />
                        <Box className="absolute top-5 right-10 bottom-0 flex items-center justify-center">
                            <Box className="w-[40px] h-[40px] bg-white rounded-full items-center justify-center">
                                <IconComponent name="arrow-down" font="feather" color={getColor("blue")} size={24} />
                            </Box>
                        </Box>
                    </Box>

                    <Box className="mx-5 mt-5">
                        <Text className="text-neutralGray text-[16px]">
                            Convert your CO2 points to Grab Rewards points and select from a wide range of services,
                            including transport, on demand food delivery, consumer services goods, and etc.
                        </Text>
                    </Box>
                </Box>
            </ScrollView>

            <Box className="px-5 py-4">
                <MyButton text="Redeem Points" onPress={handleRedeemPoints} variant="primary" />
            </Box>
        </Container>
    );
};

export default RedeemPointScreen;
