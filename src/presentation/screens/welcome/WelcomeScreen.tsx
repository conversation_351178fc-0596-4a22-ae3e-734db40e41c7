import { useNavigation } from "@react-navigation/native";
import React from "react";
import Carousel from "react-native-reanimated-carousel";

import { ButtonGetStarted } from "@/presentation/components/myButton";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container, HStack, Image, ScrollView, Text, VStack } from "@/presentation/components/ui";
import { ImageAssets, RouteName } from "@/shared/constants";
import { fullWidth } from "@/shared/helper";

const bannerData = [
    {
        source: ImageAssets.bannerOne,
        alt: "banner one",
        title: "Towards a Zero Waste Singapore.",
        description: "Proper recycling efforts within our community"
    },
    {
        source: ImageAssets.bannerTwo,
        alt: "banner two",
        title: "Recycle with us—now at more locations",
        description: "Look out for QR Codes pasted on the bins and chutes"
    },
    {
        source: ImageAssets.bannerThree,
        alt: "banner three",
        title: "Collect and redeem your points",
        description: "Recycle, earn CO₂ points, and unlock rewards with our partners"
    }
];

const WelcomeScreen = () => {
    const navigation = useNavigation();

    const [currentIndex, setCurrentIndex] = React.useState(0);

    const renderBannerItem = React.useCallback(({ item }: { item: (typeof bannerData)[number] }) => {
        return (
            <VStack className="gap-5">
                <Image source={item.source} className="w-full h-[400px]" alt={item.alt} />
                <Box className="px-5 gap-3">
                    <Text className="text-[29px] font-bold text-darkBlue">{item.title}</Text>
                    <Text className="text-lg text-darkBlue">{item.description}</Text>
                </Box>
            </VStack>
        );
    }, []);

    const handleCurrentIndexChange = React.useCallback((index: number) => {
        setCurrentIndex(index % bannerData.length);
    }, []);

    const onLogin = React.useCallback(() => {
        navigation.navigate(RouteName.Auth, { screen: RouteName.Login });
    }, [navigation]);

    return (
        <Container>
            <ScrollView>
                <Carousel
                    loop={false}
                    width={fullWidth}
                    height={550}
                    autoPlay={false}
                    data={bannerData}
                    scrollAnimationDuration={1000}
                    renderItem={renderBannerItem}
                    onScrollEnd={handleCurrentIndexChange}
                    modeConfig={{
                        parallaxScrollingScale: 0.9,
                        parallaxScrollingOffset: 50
                    }}
                    defaultIndex={currentIndex}
                />
                <HStack className="justify-center gap-2 mt-5">
                    {bannerData.map((_, index) => (
                        <Box
                            key={index}
                            className={`w-[8px] h-[8px] rounded-full ${currentIndex === index ? "bg-darkBlue" : "bg-darkBlue opacity-50"}`}
                        />
                    ))}
                </HStack>
                <Box className="px-5 mt-10 items-center gap-5">
                    <ButtonGetStarted />
                    <MyTouchable onPress={onLogin}>
                        <Text className="text-blue text-[15px] font-bold">I already have an account</Text>
                    </MyTouchable>
                </Box>
            </ScrollView>
        </Container>
    );
};

export default WelcomeScreen;
