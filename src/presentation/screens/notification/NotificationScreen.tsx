import React from "react";

import { Head<PERSON> } from "@/presentation/components/header";
import { Box, Container, HStack, Image, ScrollView, Text, VStack } from "@/presentation/components/ui";
import { ImageAssets } from "@/shared/constants";

interface NotificationItem {
    id: string;
    title: string;
    description: string;
    date: string;
    timeAgo: string;
    isRead: boolean;
}

// Mock data for notifications grouped by month
const mockNotifications: Record<string, NotificationItem[]> = {
    "MAY 2025": [
        {
            id: "1",
            title: "Welcome to the Step Up App",
            description: "Learn about the functions of the Step Up APP with our interactive tutorial",
            date: "",
            timeAgo: "1m",
            isRead: false
        },
        {
            id: "2",
            title: "Shopping for a new appliance?",
            description: "Look for the Energy Label to find out their energy efficiency rating.",
            date: "",
            timeAgo: "1d",
            isRead: false
        },
        {
            id: "3",
            title: "Check and clean your metals before recycling and earn more points!",
            description:
                "Tap to find out what metals you can recycle at the blue bins and earn more points up to 1.000 points extra",
            date: "17/05",
            timeAgo: "",
            isRead: false
        }
    ],
    "FEB 2025": [
        {
            id: "4",
            title: "Recycle now & get more points!",
            description: "Scan within the next 2 hours & earn 200 points extra",
            date: "03/02",
            timeAgo: "",
            isRead: true
        },
        {
            id: "5",
            title: "Cash For Trash stations closed during CNY",
            description: "All stations will be closed on 2 February 2025",
            date: "02/02",
            timeAgo: "",
            isRead: true
        },
        {
            id: "6",
            title: "Changes in Cash For Trash Schedule",
            description: "For more details, please refer to our website or app under Events",
            date: "01/02",
            timeAgo: "",
            isRead: true
        }
    ],
    "JAN 2025": [
        {
            id: "7",
            title: "Cash For Trash - Closure",
            description: "",
            date: "05/01",
            timeAgo: "",
            isRead: true
        }
    ]
};

const NotificationScreen = () => {
    const renderNotificationItem = (item: NotificationItem) => {
        return (
            <Box key={item.id} className="mb-4">
                <HStack className="items-start justify-between">
                    <HStack className="gap-3 flex-1">
                        <Image source={ImageAssets.icNotification} className="w-[24px] h-[24px]" alt="notification" />
                        <VStack className="gap-1 flex-1">
                            <Text className="text-darkGray text-[16px] font-medium">{item.title}</Text>
                            {item.description ? (
                                <Text className="text-neutralGray text-[14px]">{item.description}</Text>
                            ) : null}
                        </VStack>
                    </HStack>
                    <Text className="text-neutralGray text-[14px] ml-2">{item.timeAgo || item.date}</Text>
                </HStack>
                {!item.isRead && (
                    <Box className="absolute right-0 top-1/2 -translate-y-1/2 w-2 h-2 rounded-full bg-blue" />
                )}
            </Box>
        );
    };

    return (
        <Container>
            <Header
                title="Notification"
                rightComponent={<Text className="text-blue-600 text-[16px]">Mark as read</Text>}
            />

            <ScrollView className="px-4 pt-4">
                {Object.entries(mockNotifications).map(([month, notifications]) => (
                    <VStack key={month} className="mb-8">
                        <Box className="mb-4">
                            <Text className="text-neutralGray text-[16px] font-bold">{month}</Text>
                        </Box>
                        {notifications.map(renderNotificationItem)}
                    </VStack>
                ))}
            </ScrollView>
        </Container>
    );
};

export default NotificationScreen;
