import React from "react";

import { Head<PERSON> } from "@/presentation/components/header";
import { Box, Container, HStack, Image, ScrollView, Text, VStack } from "@/presentation/components/ui";
import { ImageAssets } from "@/shared/constants";

interface ActivityItem {
    id: string;
    type: string;
    date: string;
    time: string;
    co2Saved: string;
    points: string;
}

// Mock data for the activity history
const mockActivities: Record<string, ActivityItem[]> = {
    "JAN 2025": [
        {
            id: "1",
            type: "RVM Bin",
            date: "17 Jan 2025",
            time: "3:08pm",
            co2Saved: "0.1 kg CO₂",
            points: "+10.000 pts"
        },
        {
            id: "2",
            type: "Recycle Activity",
            date: "17 Jan 2025",
            time: "3:08pm",
            co2Saved: "0.1 kg CO₂",
            points: "+10.000 pts"
        },
        {
            id: "3",
            type: "Recycle Activity",
            date: "17 Jan 2025",
            time: "3:08pm",
            co2Saved: "0.1 kg CO₂",
            points: "+10.000 pts"
        },
        {
            id: "4",
            type: "Recycle Activity",
            date: "17 Jan 2025",
            time: "3:08pm",
            co2Saved: "0.1 kg CO₂",
            points: "+10.000 pts"
        }
    ],
    "DEC 2025": [
        {
            id: "5",
            type: "Recycle Activity",
            date: "17 Dec 2024",
            time: "3:08pm",
            co2Saved: "0.1 kg CO₂",
            points: "+10.000 pts"
        },
        {
            id: "6",
            type: "Recycle Activity",
            date: "17 Jan 2025",
            time: "3:08pm",
            co2Saved: "0.1 kg CO₂",
            points: "+10.000 pts"
        }
    ],
    "NOV 2025": [
        {
            id: "7",
            type: "Recycle Activity",
            date: "17 Jan 2025",
            time: "3:08pm",
            co2Saved: "0.1 kg CO₂",
            points: "+10.000 pts"
        }
    ]
};

const ActivityHistoryScreen = () => {
    const renderActivityItem = (item: ActivityItem) => {
        return (
            <Box key={item.id} className="bg-background-light rounded-xl p-4">
                <HStack alignItems="center" justifyContent="space-between">
                    <HStack className="gap-2">
                        <Box>
                            <Image source={ImageAssets.icActivity} className="w-[24px] h-[24px]" alt="activity" />
                        </Box>
                        <VStack className="gap-1">
                            <Text className="text-darkGray text-[16px]">{item.type}</Text>
                            <Text className="text-neutralGray text-[14px]">
                                {item.date} | {item.time}
                            </Text>
                        </VStack>
                    </HStack>
                    <VStack alignItems="flex-end" className="gap-1">
                        <Text className="text-darkGray font-bold text-[16px]">{item.co2Saved}</Text>
                        <Text className="text-neutralGray font-bold text-[16px]">{item.points}</Text>
                    </VStack>
                </HStack>
            </Box>
        );
    };

    return (
        <Container>
            <Header title="Activity History" />

            <ScrollView className="px-4 pt-4">
                {Object.entries(mockActivities).map(([month, activities]) => (
                    <VStack key={month} className="gap-4">
                        <Box className="pt-4">
                            <Text className="text-neutralGray text-[16px] font-bold">{month}</Text>
                        </Box>
                        {activities.map(renderActivityItem)}
                    </VStack>
                ))}
            </ScrollView>
        </Container>
    );
};

export default ActivityHistoryScreen;
