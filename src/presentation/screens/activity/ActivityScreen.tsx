import { useNavigation } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import React from "react";
import { Image, ImageBackground } from "react-native";

import { Header } from "@/presentation/components/header";
import { Box, Container, HStack, Sc<PERSON>View, Text } from "@/presentation/components/ui";
import { ImageAssets, RouteName } from "@/shared/constants";

const ActivityScreen = () => {
    const navigation = useNavigation();

    const onActivityHistory = React.useCallback(() => {
        navigation.navigate(RouteName.ActivityHistory);
    }, [navigation]);

    return (
        <Container>
            <ImageBackground source={ImageAssets.bgActivity} className="w-full h-full">
                <Box className="pt-4">
                    <Header
                        title="Activity"
                        titleColor="white"
                        isShowBack={false}
                        rightComponent={ImageAssets.activityOff}
                        tintColor="white"
                        onPress={onActivityHistory}
                    />
                </Box>
                <ScrollView className="px-3">
                    <Box className="mt-4 mb-6">
                        <Box className="bg-white/20 rounded-t-[12px] px-5 py-3 backdrop-blur-md self-start">
                            <Text className="text-white text-[13px] font-bold">Your recycling wins so far</Text>
                        </Box>
                        <Box className="bg-white/30 rounded-tr-[12px] rounded-b-[12px] p-5 backdrop-blur-md">
                            <Box className="absolute top-0 left-0 right-0 h-[1px]">
                                <LinearGradient
                                    colors={["transparent", "rgba(255,255,255,0.6)", "transparent"]}
                                    start={{ x: 0, y: 0.5 }}
                                    end={{ x: 1, y: 0.5 }}
                                    style={{ height: 1, width: "100%" }}
                                />
                            </Box>
                            <Box className="flex-row items-center">
                                <Image source={ImageAssets.trophy} className="w-16 h-16 mr-4" alt="trophy" />
                                <Box className="gap-1">
                                    <Text className="text-white text-[24px] font-bold">0.2 kg</Text>
                                    <Text className="text-white text-[13px]">
                                        Your total CO<Text className="text-[13px] align-bottom">2</Text> emission saved
                                    </Text>
                                </Box>
                            </Box>
                            <Box className="absolute bottom-0 left-0 right-0 h-[1px]">
                                <LinearGradient
                                    colors={["transparent", "rgba(255,255,255,0.6)", "transparent"]}
                                    start={{ x: 0, y: 0.5 }}
                                    end={{ x: 1, y: 0.5 }}
                                    style={{ height: 1, width: "100%" }}
                                />
                            </Box>
                        </Box>

                        <Box className="mt-6 gap-3">
                            <HStack className="justify-between items-center">
                                <HStack className="items-center gap-2">
                                    <Box className="bg-white/30 rounded-full p-1 backdrop-blur-md self-start">
                                        <Image source={ImageAssets.crown} className="w-[18px] h-[18px]" alt="crown" />
                                    </Box>
                                    <Text className="text-white text-[13px] font-bold">#128973</Text>
                                </HStack>
                                <Text className="text-white text-[13px] font-bold">of 223565 contributors</Text>
                            </HStack>

                            <Box className="gap-3 bg-white rounded-xl p-4">
                                <Text className="text-darkGray text-[16px] font-bold">Top Contributors</Text>
                                {[1, 2, 3, 4].map((item, index) => {
                                    const isMe = index === 3;
                                    return (
                                        <Box
                                            key={item}
                                            className={`bg-white/20 rounded-xl backdrop-blur-md p-2 ${
                                                isMe ? "bg-lightBlue" : ""
                                            }`}>
                                            <Box className="flex-row justify-between items-center">
                                                <HStack className="items-center">
                                                    <Box
                                                        className={`bg-gray rounded-full w-[40px] h-[40px] items-center justify-center mr-3 ${
                                                            isMe ? "bg-blue" : ""
                                                        }`}>
                                                        <Text
                                                            className={`text-[13px] font-bold ${
                                                                isMe ? "text-white" : ""
                                                            }`}>
                                                            AN
                                                        </Text>

                                                        {!isMe && (
                                                            <Box className="absolute top-[-5px] right-[-5px] ">
                                                                <Box>
                                                                    <Image
                                                                        source={ImageAssets.rank}
                                                                        className="w-[18px] h-[18px]"
                                                                        alt="rank"
                                                                    />
                                                                    <Box className="absolute top-0 left-0 right-0 bottom-0  rounded-full w-[18px] h-[18px] items-center justify-center">
                                                                        <Text className="text-[13px] font-bold text-white">
                                                                            {item}
                                                                        </Text>
                                                                    </Box>
                                                                </Box>
                                                            </Box>
                                                        )}
                                                    </Box>
                                                    <Box>
                                                        <Text className="text-black font-semibold">Anonymous</Text>
                                                    </Box>
                                                </HStack>
                                                <HStack className="items-center gap-1">
                                                    <Text className="text-darkGray font-bold text-[16px]">1050</Text>
                                                    <Text className="text-darkGray text-[13px]">kg CO2</Text>
                                                </HStack>
                                            </Box>
                                        </Box>
                                    );
                                })}
                            </Box>
                        </Box>
                    </Box>
                </ScrollView>
            </ImageBackground>
        </Container>
    );
};

export default ActivityScreen;
