import React from "react";

import { EmptyState } from "@/presentation/components/emptyState";
import { Header } from "@/presentation/components/header";
import { Box, Container, Image, ScrollView, Text, VStack } from "@/presentation/components/ui";
import { ImageAssets } from "@/shared/constants";

type RewardHistoryItem = {
    id: string;
    type: "earn" | "redeem";
    points: number;
    date: string;
    time: string;
};

// Group data by month
const DUMMY_DATA: Record<string, RewardHistoryItem[]> = {
    "JAN 2025": [
        { id: "1", type: "earn", points: 10000, date: "17 Jan 2025", time: "3:08pm" },
        { id: "2", type: "earn", points: 10000, date: "11 Jan 2025", time: "3:08pm" },
        { id: "3", type: "redeem", points: 10000, date: "5 Jan 2025", time: "3:08pm" },
        { id: "4", type: "redeem", points: 10000, date: "1 Jan 2025", time: "3:08pm" }
    ],
    "DEC 2024": [
        { id: "5", type: "earn", points: 10000, date: "25 Dec 2024", time: "3:08pm" },
        { id: "6", type: "earn", points: 10000, date: "20 Dec 2024", time: "3:08pm" }
    ],
    "NOV 2024": [{ id: "7", type: "earn", points: 10000, date: "10 Nov 2024", time: "3:08pm" }]
};

const RewardHistoryItem: React.FC<{ item: RewardHistoryItem }> = ({ item }) => {
    return (
        <Box className="flex-row justify-between items-center p-4 bg-background-light rounded-lg mb-4">
            <Box className="flex-row items-start gap-2">
                <Image source={ImageAssets.icPoint} className="w-[24px] h-[24px]" alt="Point" />
                <Box>
                    <Text className="font-medium text-base">
                        {item.type === "earn" ? "Earn points" : "Points redeemed"}
                    </Text>
                    <Text className="text-neutralGray text-sm">
                        {item.date} | {item.time}
                    </Text>
                </Box>
            </Box>
            <Text className={`font-bold text-base ${item.type === "earn" ? "text-darkBlue" : "text-red"}`}>
                {item.type === "earn" ? "+" : "-"}
                {item.points.toLocaleString()}
            </Text>
        </Box>
    );
};

const RewardsHistoryScreen = () => {
    const hasData = Object.keys(DUMMY_DATA).length > 0;

    const renderEmptyState = React.useMemo(() => {
        return (
            <EmptyState
                title="You haven't earned CO2 points yet"
                description="Let's earn it by start recycling your goods"
                buttonText="Start recycling"
            />
        );
    }, []);

    return (
        <Container className="bg-background-light">
            <Header title="Rewards History" />

            {hasData ? (
                <ScrollView className="px-4 pt-4">
                    {Object.entries(DUMMY_DATA).map(([month, rewards]) => (
                        <VStack key={month} className="mb-8">
                            <Box className="mb-4">
                                <Text className="text-neutralGray text-[16px] font-bold">{month}</Text>
                            </Box>
                            {rewards.map((item) => (
                                <RewardHistoryItem key={item.id} item={item} />
                            ))}
                        </VStack>
                    ))}
                </ScrollView>
            ) : (
                renderEmptyState
            )}
        </Container>
    );
};

export default RewardsHistoryScreen;
