import React from "react";

import { useLogin } from "@/presentation/hooks";

import { <PERSON><PERSON> } from "@/presentation/components/header";
import { Input } from "@/presentation/components/input";
import { MyButton } from "@/presentation/components/myButton";
import { OptionLogin } from "@/presentation/components/optionLogin";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container, ScrollView, Text } from "@/presentation/components/ui";

const LoginScreen = () => {
    const {
        getInputProps,
        handleSubmit,
        isLoading,
        error,
        handleForgotPassword,
        handleOrganization,
        titleChange,
        isOrganization
    } = useLogin();

    return (
        <Container>
            <Header title="Login to ALBA Step Up" />
            <ScrollView className="px-4">
                <Box className="gap-4 ">
                    <Input
                        title={titleChange[1]}
                        placeholder={titleChange[2]}
                        editable={!isLoading}
                        {...{
                            ...getInputProps(isOrganization ? "username" : "email"),
                            error: getInputProps(isOrganization ? "username" : "email").error || error
                        }}
                        autoCapitalize="none"
                    />
                    <Input
                        title="Password"
                        placeholder="Enter your password"
                        secureTextEntry
                        editable={!isLoading}
                        {...{
                            ...getInputProps("password"),
                            error: getInputProps("password").error || error
                        }}
                    />
                    <MyButton text="Login" className="mt-3 mb-3" onPress={handleSubmit} isLoading={isLoading} />
                    <MyTouchable onPress={handleForgotPassword} disabled={isLoading}>
                        <Text className="text-blue font-bold text-center">Forgot Password?</Text>
                    </MyTouchable>
                </Box>
            </ScrollView>

            <Box className="p-4">
                <OptionLogin title={titleChange[0]} onPress={handleOrganization} />
            </Box>
        </Container>
    );
};

export default LoginScreen;
