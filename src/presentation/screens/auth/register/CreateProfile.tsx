import React from "react";
import { object } from "yup";

import { useRegisterStore } from "@/app/store";

import { useForm, usePostalCodeValidation } from "@/presentation/hooks";

import { businessDrop } from "@/data/local";
import { Input } from "@/presentation/components/input";
import { Text, VStack } from "@/presentation/components/ui";
import BusinessType from "@/shared/constants/BusinessType";
import {
    addressSchema,
    businessTypeSchema,
    dobSchema,
    firstNameSchema,
    lastNameSchema,
    postalCodeSchema,
    schoolNameSchema
} from "@/shared/validations";

type CreateProfileProps = {
    isSchool?: boolean;
    onNextStep?: () => void;
    initialValues?: RegisterRequest["individual"] | RegisterRequest["school"];
};

export type CreateProfileRefType = {
    onSubmit: () => void;
};

const CreateProfile = React.forwardRef<CreateProfileRefType, CreateProfileProps>(
    ({ isSchool, onNextStep, initialValues }, ref) => {
        const { setIndividual, setSchool, request } = useRegisterStore();
        const { isValidating, handlePostalCodeChange } = usePostalCodeValidation();

        const getInitialValues = React.useMemo(() => {
            const school = initialValues as RegisterRequest["school"];
            const individual = initialValues as RegisterRequest["individual"];

            const schoolValues = {
                name: school?.name || "",
                type: school?.type || ""
            };

            const individualValues = {
                firstName: individual?.firstName || "",
                lastName: individual?.lastName || "",
                dob: individual?.dob || ""
            };

            const commonValues = {
                address: school?.address || individual?.address || "",
                postalCode: school?.postalCode || individual?.postalCode || ""
            };

            return isSchool
                ? {
                      ...schoolValues,
                      ...commonValues
                  }
                : {
                      ...individualValues,
                      ...commonValues
                  };
        }, [initialValues, isSchool]);

        const validationSchema = React.useMemo(() => {
            const commonSchema = object().shape({
                address: addressSchema,
                postalCode: postalCodeSchema
            });

            if (isSchool) {
                return object()
                    .shape({
                        name: schoolNameSchema,
                        type: businessTypeSchema
                    })
                    .concat(commonSchema);
            }
            return object()
                .shape({
                    firstName: firstNameSchema,
                    lastName: lastNameSchema,
                    dob: dobSchema
                })
                .concat(commonSchema);
        }, [isSchool]);

        const { getInputProps, handleSubmit, setFieldTouched, validateForm } = useForm({
            enableReinitialize: true,
            initialValues: getInitialValues,
            validationSchema,
            onSubmit: (values) => {
                if (isSchool) {
                    const schoolValues = values as unknown as RegisterRequest["school"];
                    setSchool({
                        ...request?.school,
                        name: schoolValues?.name,
                        address: schoolValues?.address,
                        postalCode: schoolValues?.postalCode,
                        type: Number(schoolValues?.type) === 1 ? BusinessType.School : BusinessType.Enterprise
                    });
                } else {
                    const individualValues = values as unknown as RegisterRequest["individual"];
                    setIndividual({
                        ...request?.individual,
                        firstName: individualValues?.firstName,
                        lastName: individualValues?.lastName,
                        dob: individualValues?.dob,
                        address: individualValues?.address,
                        postalCode: individualValues?.postalCode
                    });
                }
                onNextStep?.();
            }
        });

        React.useImperativeHandle(ref, () => ({
            onSubmit: handleSubmit
        }));

        const handlePostalCodeInputChange = React.useCallback(
            (field: string, value: string) => {
                const baseProps = getInputProps("postalCode" as keyof typeof getInitialValues);
                handlePostalCodeChange(field, value, baseProps.onChangeValue!, setFieldTouched, validateForm);
            },
            [getInputProps, handlePostalCodeChange, setFieldTouched, validateForm]
        );

        const renderContent = React.useMemo(() => {
            if (isSchool) {
                return (
                    <>
                        <Input
                            title="School Name"
                            placeholder="Enter school name"
                            required
                            {...getInputProps("name" as keyof typeof getInitialValues)}
                        />
                        <Input
                            title="Address"
                            placeholder="Address"
                            required
                            {...getInputProps("address" as keyof typeof getInitialValues)}
                        />
                        <Input
                            title="Postal Code"
                            placeholder="Postal Code"
                            required
                            {...{
                                ...getInputProps("postalCode" as keyof typeof getInitialValues),
                                onChangeValue: handlePostalCodeInputChange
                            }}
                            keyboardType="numeric"
                            isLoading={isValidating}
                        />
                        <Input
                            title="School/Enterprise"
                            placeholder="Select school/enterprise"
                            type="dropdown"
                            required
                            dataDropdown={businessDrop}
                            {...getInputProps("type" as keyof typeof getInitialValues)}
                        />
                    </>
                );
            }
            return (
                <>
                    <Input
                        title="First Name"
                        placeholder="Enter first name"
                        required
                        {...getInputProps("firstName" as keyof typeof getInitialValues)}
                    />
                    <Input
                        title="Last Name"
                        placeholder="Enter last name"
                        required
                        {...getInputProps("lastName" as keyof typeof getInitialValues)}
                    />
                    <Input
                        title="Date of birth"
                        type="date"
                        required
                        {...getInputProps("dob" as keyof typeof getInitialValues)}
                    />
                    <Input
                        title="Address"
                        placeholder="Address"
                        required
                        {...getInputProps("address" as keyof typeof getInitialValues)}
                    />
                    <Input
                        title="Postal Code"
                        placeholder="Postal Code"
                        required
                        {...{
                            ...getInputProps("postalCode" as keyof typeof getInitialValues),
                            onChangeValue: handlePostalCodeInputChange
                        }}
                        keyboardType="numeric"
                        isLoading={isValidating}
                        suffixIcon={isValidating ? <Text className="text-gray2">Validating...</Text> : undefined}
                    />
                </>
            );
        }, [isSchool, getInputProps, isValidating, handlePostalCodeInputChange]);

        return (
            <VStack space="lg" className="mb-10">
                <Text className="text-[24px] font-bold">Create profile</Text>
                <VStack className="gap-5">{renderContent}</VStack>
            </VStack>
        );
    }
);

export default CreateProfile;
