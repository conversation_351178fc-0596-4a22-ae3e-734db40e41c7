import { useNavigation } from "@react-navigation/native";
import React from "react";

import { Header } from "@/presentation/components/header";
import { MyButton } from "@/presentation/components/myButton";
import { Box, Container, HStack, Image, ScrollView, Text, VStack } from "@/presentation/components/ui";
import { ImageAssets, RouteName } from "@/shared/constants";

type BenefitCardProps = {
    title: string;
    description: string;
};

const BenefitCard: React.FC<BenefitCardProps> = ({ title, description }) => {
    return (
        <Box className="rounded-xl overflow-hidden border border-lightGray">
            <HStack className="bg-[#f2f9ed] p-4 items-center">
                <Box className="w-14 h-14 rounded-full bg-white" />
                <VStack className="p-4">
                    <Text className="font-bold text-[16px]">{title}</Text>
                    <Text>{description}</Text>
                </VStack>
            </HStack>
        </Box>
    );
};

const WelcomeSchool = () => {
    const navigation = useNavigation();
    const benefits: BenefitCardProps[] = [
        {
            title: "Recognition & Incentives",
            description: "Earn recognition through ALBA’s school engagement programs."
        },
        {
            title: "Access to Recycling Infrastructure",
            description: "Convenient recycling bins and collection services provided by ALBA."
        },
        {
            title: "Community Impact",
            description: "Encourage a recycling culture beyond the classroom—among parents and the local community."
        }
    ];

    const handleJoinTheChange = React.useCallback(() => {
        navigation.navigate(RouteName.Register, { isSchool: true });
    }, [navigation]);

    return (
        <Container>
            <Header title="Schools and Community" />
            <ScrollView>
                <Image
                    source={ImageAssets.bannerSchool}
                    className="w-full h-[100px]"
                    alt="bannerSchool"
                    resizeMode="cover"
                />
                <Box className="px-4 ">
                    <Box className="items-center gap-2">
                        <Text className="text-[22px] font-bold">Recycle Right. Lead the Change</Text>
                        <Text className="text-center">
                            Supporting schools and communities in building better recycling habits
                        </Text>
                    </Box>
                    <Box className="pt-10 pb-10">
                        <Text>Benefits of Recycling with ALBA</Text>

                        <VStack space="md" className="gap-4">
                            {benefits.map((benefit, index) => (
                                <BenefitCard key={index} title={benefit.title} description={benefit.description} />
                            ))}
                        </VStack>
                    </Box>
                </Box>
            </ScrollView>
            <Box className="p-4">
                <MyButton text="Join The Change" onPress={handleJoinTheChange} />
            </Box>
        </Container>
    );
};

export default WelcomeSchool;
