import React from "react";

import { useRegister } from "@/presentation/hooks";

import CreateProfile from "./CreateProfile";
import EmailAndPassword from "./EmailAndPassword";
import PhoneNumber from "./PhoneNumber";

import { CheckBox } from "@/presentation/components/checkBox";
import { Header } from "@/presentation/components/header";
import { MyButton } from "@/presentation/components/myButton";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container, HStack, ScrollView } from "@/presentation/components/ui";

const RegisterScreen = () => {
    const {
        step,
        nextStep,
        isSchool,
        isChecked,
        setIsChecked,
        shouldShowCheckbox,
        isLoadingPage,
        isNextButtonDisabled,
        request,
        handleNext,
        handleStep,
        createProfileRef,
        emailAndPasswordRef,
        phoneNumberRef
    } = useRegister();

    const renderSchoolStepIndicator = React.useCallback(() => {
        return (
            <HStack className="w-full justify-between flex-1 gap-2">
                <MyTouchable className="h-2 rounded-full bg-green flex-1" onPress={handleStep(0)} />
                <MyTouchable
                    className={`h-2 rounded-full ${step >= 1 ? "bg-green" : "bg-lightGray"} flex-1`}
                    onPress={handleStep(1)}
                />
            </HStack>
        );
    }, [step, handleStep]);

    const renderIndividualStepIndicator = React.useCallback(() => {
        return (
            <HStack className="w-full justify-between flex-1 gap-2">
                <MyTouchable className="h-2 rounded-full bg-green flex-1" onPress={handleStep(0)} />
                <MyTouchable
                    className={`h-2 rounded-full ${step >= 1 ? "bg-green" : "bg-lightGray"} flex-1`}
                    onPress={handleStep(1)}
                />
                <MyTouchable
                    className={`h-2 rounded-full ${step >= 2 ? "bg-green" : "bg-lightGray"} flex-1`}
                    onPress={handleStep(2)}
                />
            </HStack>
        );
    }, [step, handleStep]);

    const renderTitle = React.useMemo(() => {
        return isSchool ? renderSchoolStepIndicator() : renderIndividualStepIndicator();
    }, [isSchool, renderSchoolStepIndicator, renderIndividualStepIndicator]);

    const renderContent = React.useMemo(() => {
        const initialValues = request?.individual || request?.school;

        switch (step) {
            case 0:
                return (
                    <CreateProfile
                        isSchool={isSchool}
                        ref={createProfileRef}
                        onNextStep={nextStep}
                        initialValues={initialValues}
                    />
                );
            case 1:
                return (
                    <EmailAndPassword
                        isSchool={isSchool}
                        ref={emailAndPasswordRef}
                        onNextStep={nextStep}
                        initialValues={initialValues}
                    />
                );
            case 2:
                return <PhoneNumber ref={phoneNumberRef} onNextStep={nextStep} initialValues={request?.individual} />;
            default:
                return null;
        }
    }, [
        request?.individual,
        request?.school,
        step,
        isSchool,
        createProfileRef,
        nextStep,
        emailAndPasswordRef,
        phoneNumberRef
    ]);

    const renderCheckBox = React.useMemo(() => {
        return shouldShowCheckbox ? <CheckBox onChange={setIsChecked} value={isChecked} /> : null;
    }, [shouldShowCheckbox, setIsChecked, isChecked]);

    return (
        <Container isLoading={isLoadingPage}>
            <Header title={renderTitle} />
            <ScrollView className="px-4">{renderContent}</ScrollView>

            <Box className="px-4 gap-3 py-4">
                {renderCheckBox}
                <MyButton text="Next" onPress={handleNext} disabled={isNextButtonDisabled} />
            </Box>
        </Container>
    );
};

export default RegisterScreen;
