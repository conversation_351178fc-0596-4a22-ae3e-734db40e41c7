import React from "react";
import { object } from "yup";

import { useRegisterStore } from "@/app/store";

import { useForm } from "@/presentation/hooks";

import { Input } from "@/presentation/components/input";
import { Text, VStack } from "@/presentation/components/ui";
import { formatPhoneNumber, splitPhoneNumber } from "@/shared/helper";
import { phoneNumberSchema } from "@/shared/validations";

type PhoneNumberProps = {
    onNextStep?: () => void;
    initialValues?: RegisterRequest["individual"];
};

export type PhoneNumberRefType = {
    onSubmit: () => void;
};

const PhoneNumber = React.forwardRef<PhoneNumberRefType, PhoneNumberProps>(({ onNextStep, initialValues }, ref) => {
    const { request, setIndividual } = useRegisterStore();

    const initValues = React.useMemo(() => {
        const { number } = splitPhoneNumber(initialValues?.phone ?? "");

        return {
            phoneNumber: number || ""
        };
    }, [initialValues]);

    const { getInputProps, handleSubmit } = useForm({
        enableReinitialize: true,
        initialValues: initValues,
        validationSchema: object().shape({
            phoneNumber: phoneNumberSchema
        }),
        onSubmit: (values) => {
            const formattedPhone = formatPhoneNumber({ phoneNumber: values.phoneNumber });

            setIndividual({
                ...request?.individual,
                phone: formattedPhone
            });

            onNextStep?.();
        }
    });

    React.useImperativeHandle(ref, () => ({
        onSubmit: handleSubmit
    }));

    return (
        <VStack space="lg" className="mb-10">
            <Text className="text-[24px] font-bold">Phone number</Text>
            <VStack className="gap-5">
                <Input
                    title="Phone number"
                    placeholder="Enter phone number"
                    required
                    type="phone"
                    {...getInputProps("phoneNumber")}
                />
            </VStack>
        </VStack>
    );
});

export default PhoneNumber;
