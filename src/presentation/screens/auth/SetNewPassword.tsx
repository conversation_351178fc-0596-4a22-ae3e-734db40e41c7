import React from "react";
import { object } from "yup";

import { useUpdatePasswordQueries } from "@/data/queries";

import { useForm, useRouteParams } from "@/presentation/hooks";

import { Header } from "@/presentation/components/header";
import { Input } from "@/presentation/components/input";
import { ButtonSubmitVerify } from "@/presentation/components/myButton";
import { Box, Container, ScrollView, Text } from "@/presentation/components/ui";
import { ImageAssets, RouteName } from "@/shared/constants";
import { formatPhoneWithCountryCode } from "@/shared/helper";
import { confirmPasswordSchema, passwordSchema } from "@/shared/validations";

const SetNewPassword = () => {
    const params = useRouteParams<typeof RouteName.SetNewPassword>();
    const { updatePassword, isLoading } = useUpdatePasswordQueries();

    const { getInputProps, handleSubmit } = useForm({
        initialValues: {
            password: "",
            confirmPassword: ""
        },
        validationSchema: object().shape({
            password: passwordSchema,
            confirmPassword: confirmPasswordSchema("password")
        }),
        onSubmit: (values) => {
            if (!params?.phoneNumber) return;
            updatePassword({
                ...values,
                password_confirmation: values.confirmPassword,
                phone: formatPhoneWithCountryCode({ phone: params?.phoneNumber })
            });
        }
    });

    return (
        <Container>
            <Header title="Set New Password" rightComponent={ImageAssets.icInfo} />
            <ScrollView className="px-4 pt-5">
                <Text>Please set your new password</Text>
                <Box className="mt-4 gap-y-4">
                    <Input
                        title="New Password"
                        placeholder="New Password"
                        secureTextEntry
                        required
                        {...getInputProps("password")}
                        editable={!isLoading}
                    />
                    <Input
                        title="Confirm Password"
                        placeholder="Confirm Password"
                        secureTextEntry
                        required
                        {...getInputProps("confirmPassword")}
                        editable={!isLoading}
                    />
                </Box>
            </ScrollView>
            <ButtonSubmitVerify handleVerification={handleSubmit} needHelp={false} isLoading={isLoading} />
        </Container>
    );
};

export default SetNewPassword;
