import React from "react";

import { useVerification } from "@/presentation/hooks";

import { Head<PERSON> } from "@/presentation/components/header";
import { Input } from "@/presentation/components/input";
import { ButtonSubmitVerify } from "@/presentation/components/myButton";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container, HStack, ScrollView, Text } from "@/presentation/components/ui";
import { ImageAssets } from "@/shared/constants";
import { formatPhoneWithCountryCode } from "@/shared/helper";

const VerificationScreen: React.FC = () => {
    const {
        isEnable,
        timeFormatted,
        otpRef,
        isLoadingOtp,
        isLoadingRegister,
        isLoadingRegisterSchool,
        getInputProps,
        handleSubmit,
        handleResendOtp,
        params
    } = useVerification();

    const isLoadingPage = React.useMemo(
        () => isLoadingOtp || isLoadingRegister || isLoadingRegisterSchool,
        [isLoadingOtp, isLoadingRegister, isLoadingRegisterSchool]
    );

    return (
        <Container isLoading={isLoadingPage}>
            <Header title="Verification" rightComponent={ImageAssets.icInfo} />
            <ScrollView className="px-4 pt-5">
                <Text className="text-center">
                    {`We have sent you an verification OTP on \n ${formatPhoneWithCountryCode({
                        phone: params?.phoneNumber,
                        noSpace: false
                    })} to complete verification`}
                </Text>
                <Box className="px-5 mt-10">
                    <Input ref={otpRef} type="otp" {...getInputProps("otp")} />
                    {isEnable ? (
                        <MyTouchable onPress={handleResendOtp} disabled={isLoadingPage}>
                            <Text className="text-center text-blue">Resend</Text>
                        </MyTouchable>
                    ) : (
                        <Text className="text-center ">You can resend your code in {timeFormatted}</Text>
                    )}
                    <HStack className="items-center justify-center gap-x-2 mt-5">
                        <Text>Wrong phone number?</Text>
                        <MyTouchable disabled={isLoadingPage}>
                            <Text className="text-blue">Change</Text>
                        </MyTouchable>
                    </HStack>
                </Box>
            </ScrollView>
            <Box className="py-4">
                <ButtonSubmitVerify handleVerification={handleSubmit} />
            </Box>
        </Container>
    );
};

export default VerificationScreen;
