import React from "react";
import { Image } from "react-native";

import { Header } from "@/presentation/components/header";
import { ButtonSubmitVerify } from "@/presentation/components/myButton";
import { Box, Container, HStack, Text, VStack } from "@/presentation/components/ui";
import { ImageAssets } from "@/shared/constants";

const SchoolsRegistration = () => {
    const caseNumber = "#2355";

    return (
        <Container>
            <Header title="Schools Registration" />
            <VStack className="flex-1 px-4 justify-between">
                <VStack className="items-center mt-4 gap-y-8">
                    <Image
                        source={ImageAssets.bannerSchoolRegistration}
                        style={{ width: "100%", height: 200, resizeMode: "contain" }}
                    />

                    <Box className="items-center gap-y-2">
                        <Text className="text-xl font-bold mb-2">Thank you for your enquiry</Text>
                        <Text className="text-center">
                            Our representative has received your form and will be in touch shortly. We appreciate your
                            patience.
                        </Text>
                    </Box>

                    <Box className="items-center gap-y-2">
                        <HStack className="w-full justify-between bg-gray p-5 rounded-lg">
                            <Text className="text-gray-700">Your Case No.</Text>
                            <Text className="text-blue font-bold">{caseNumber}</Text>
                        </HStack>

                        <Text className="text-red text-sm self-start">*Please submit only once</Text>
                    </Box>
                </VStack>

                <ButtonSubmitVerify title="Done" />
            </VStack>
        </Container>
    );
};

export default SchoolsRegistration;
