import { useNavigation } from "@react-navigation/native";
import React from "react";
import { object } from "yup";

import { useForm } from "@/presentation/hooks";

import { FirebaseAuthService } from "@/data/services/firebase";
import { Header } from "@/presentation/components/header";
import { Input } from "@/presentation/components/input";
import { ButtonSubmitVerify } from "@/presentation/components/myButton";
import { Box, Container, ScrollView, Text } from "@/presentation/components/ui";
import { ImageAssets, RouteName } from "@/shared/constants";
import { formatPhoneNumber } from "@/shared/helper";
import { phoneNumberSchema } from "@/shared/validations";

const ForgotPasswordScreen = () => {
    const navigation = useNavigation();
    const [isLoading, setIsLoading] = React.useState<boolean>(false);

    const { getInputProps, handleSubmit } = useForm({
        initialValues: {
            phoneNumber: ""
        },
        validationSchema: object().shape({
            phoneNumber: phoneNumberSchema
        }),
        onSubmit: async (values) => {
            const formattedPhone = formatPhoneNumber({ phoneNumber: values.phoneNumber });

            try {
                setIsLoading(true);

                const isSent = await FirebaseAuthService.sendOTP(formattedPhone);
                if (!isSent) return;

                navigation.navigate(RouteName.Verification, {
                    phoneNumber: formattedPhone,
                    type: "forgotPassword"
                });
            } catch (e) {
                /* empty */
            } finally {
                setIsLoading(false);
            }
        }
    });

    return (
        <Container>
            <Header title="Forgot Password" rightComponent={ImageAssets.icInfo} />
            <ScrollView className="px-4 pt-5">
                <Text>
                    Enter your registered phone number to reset your password. An OTP will be sent to you for
                    verification
                </Text>
                <Box className="mt-4">
                    <Input
                        title="Phone number"
                        placeholder="Phone number"
                        type="phone"
                        required
                        {...getInputProps("phoneNumber")}
                        editable={!isLoading}
                    />
                </Box>
            </ScrollView>
            <Box className="py-4">
                <ButtonSubmitVerify handleVerification={handleSubmit} isLoading={isLoading} />
            </Box>
        </Container>
    );
};

export default ForgotPasswordScreen;
