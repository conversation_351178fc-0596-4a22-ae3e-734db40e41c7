import { useNavigation } from "@react-navigation/native";
import React from "react";
import { StyleSheet } from "react-native";
import { Camera, useCameraDevice } from "react-native-vision-camera";

import { useRouteParams } from "@/presentation/hooks";

import { CameraEmptyPermission } from "@/presentation/components/emptyState";
import TookPhotoFrameOverlay from "@/presentation/components/frame/TookPhotoFrameOverlay";
import { Header } from "@/presentation/components/header";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container } from "@/presentation/components/ui";
import TookPhotoWalkthrough from "@/presentation/components/walkthrough/TookPhotoWalkthrough";
import { useCameraPermission } from "@/presentation/hooks/camera";
import { ImageAssets, RouteName } from "@/shared/constants";

let hasShownTookPhotoWalkthrough = false;

const TookPhotoScreen = () => {
    const params = useRouteParams<typeof RouteName.TookPhoto>();

    const { permissionState, requestPermission, onError } = useCameraPermission();
    const hasPermission = permissionState.isGranted;
    const [isActive, setIsActive] = React.useState(true);
    const [isTorchOn, setIsTorchOn] = React.useState(false);
    const [isWalkthroughVisible, setIsWalkthroughVisible] = React.useState(false);
    const cameraRef = React.useRef<Camera>(null);
    const device = useCameraDevice("back");
    const navigation = useNavigation();

    React.useEffect(() => {
        if (!hasPermission) return;

        const initWalkthrough = async () => {
            if (params?.fromWalkthrough && !hasShownTookPhotoWalkthrough) {
                hasShownTookPhotoWalkthrough = true;
                setIsWalkthroughVisible(true);
            }
        };

        initWalkthrough();

        return () => {
            setIsActive(false);
        };
    }, [hasPermission, params?.fromWalkthrough]);

    const handleCloseWalkthrough = React.useCallback(() => {
        setIsWalkthroughVisible(false);
    }, []);

    const [isTakingPhoto, setIsTakingPhoto] = React.useState(false);

    const takePhoto = React.useCallback(async () => {
        if (isTakingPhoto || !cameraRef.current) return;

        try {
            setIsTakingPhoto(true);

            const photo = await cameraRef.current.takePhoto({
                flash: "off",
                enableShutterSound: false
            });

            navigation.navigate(RouteName.ScanStack, {
                screen: RouteName.ConfirmPhoto,
                params: { photoPath: photo.path, binId: params?.binId }
            });

            setIsTakingPhoto(false);
        } catch (error) {
            setIsTakingPhoto(false);
            console.error("Failed to take photo:", error);
        }
    }, [isTakingPhoto, navigation, params?.binId]);

    const handleTurnOnOffLight = React.useCallback(() => {
        setIsTorchOn((prev) => !prev);
    }, []);

    if (!device || !hasPermission) {
        return <CameraEmptyPermission device={device} requestPermission={requestPermission} />;
    }

    return (
        <Container>
            <Box className="flex-1 bg-black">
                <Camera
                    ref={cameraRef}
                    style={StyleSheet.absoluteFill}
                    device={device}
                    isActive={isActive}
                    torch={isTorchOn ? "on" : "off"}
                    photo={true}
                    video={false}
                    audio={false}
                    enableZoomGesture={false}
                    onError={onError}
                />

                <Box className="absolute top-5 left-0 right-0 z-10">
                    <Header
                        title="Took Photo"
                        icBack={ImageAssets.icClose}
                        titleColor="white"
                        rightComponent={ImageAssets.icLight}
                        onPress={handleTurnOnOffLight}
                    />
                </Box>

                <TookPhotoWalkthrough
                    isVisible={isWalkthroughVisible}
                    onClose={handleCloseWalkthrough}
                    currentStep={3}
                    totalSteps={4}>
                    <TookPhotoFrameOverlay />
                </TookPhotoWalkthrough>

                <Box className="absolute bottom-10 left-0 right-0 items-center justify-center z-10">
                    <MyTouchable disabled={isTakingPhoto} onPress={takePhoto}>
                        <Box className="w-[64px] h-[64px] rounded-full bg-white items-center justify-center">
                            <Box className="w-[56px] h-[56px] rounded-full border-2 border-gray-300" />
                        </Box>
                    </MyTouchable>
                </Box>
            </Box>
        </Container>
    );
};

export default TookPhotoScreen;
