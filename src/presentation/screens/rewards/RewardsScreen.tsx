import { useNavigation } from "@react-navigation/native";
import { ListRenderItem } from "@shopify/flash-list";
import React from "react";

import RewardCard from "../../components/card/RewardCard";
import { Box, Container, Text } from "../../components/ui";

import { Header } from "@/presentation/components/header";
import { ListView } from "@/presentation/components/listView";
import { VoucherCard } from "@/presentation/components/voucherCard";
import { ImageAssets, RouteName } from "@/shared/constants";

interface VoucherItem {
    id: string;
    bgColor: string;
    title: string;
    value: string;
    points: string;
    isBlack?: boolean;
    isGreen?: boolean;
}

const RewardsScreen = () => {
    const navigation = useNavigation();
    const vouchers: VoucherItem[] = [
        { id: "1", bgColor: "#E91E63", title: "foodpanda", value: "$0.5", points: "1000" },
        { id: "2", bgColor: "#4CAF50", title: "GrabRewards", value: "550 Grab Points", points: "2000" },
        { id: "3", bgColor: "#FFFFFF", title: "stojo", value: "10%", points: "1000", isBlack: true },
        { id: "4", bgColor: "#000000", title: "anywheel", value: "20%", points: "2000", isGreen: true }
    ];

    const onPressVoucher = React.useCallback(() => {
        navigation.navigate(RouteName.RedeemPoint);
    }, [navigation]);

    const renderVoucherItem: ListRenderItem<VoucherItem> = ({ item }) => (
        <VoucherCard
            bgColor={item.bgColor}
            title={item.title}
            value={item.value}
            points={item.points}
            isBlack={item.isBlack}
            isGreen={item.isGreen}
            onPress={onPressVoucher}
        />
    );

    const onHistoryPress = React.useCallback(() => {
        navigation.navigate(RouteName.RewardsHistory);
    }, [navigation]);

    return (
        <Container>
            <Header
                title="Rewards"
                isShowBack={false}
                rightComponent={ImageAssets.activityOff}
                tintColor="black"
                onPress={onHistoryPress}
            />

            <Box className="flex-1">
                <RewardCard />

                <Box className="px-3 flex-1">
                    <ListView
                        keyList="id"
                        listHeaderComponent={
                            <Box className="px-4">
                                <Text className="mb-4 text-[16px]">
                                    Redeem your points for vouchers and enjoy great savings on your next purchase!
                                </Text>
                            </Box>
                        }
                        data={vouchers}
                        renderItem={renderVoucherItem}
                        numColumns={2}
                    />
                </Box>
            </Box>
        </Container>
    );
};

export default RewardsScreen;
