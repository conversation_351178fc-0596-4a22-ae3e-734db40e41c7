import { ListRenderItem } from "@shopify/flash-list";
import React from "react";

import { EmptyState } from "@/presentation/components/emptyState";
import { Header } from "@/presentation/components/header";
import { ListView } from "@/presentation/components/listView";
import { Box, Container } from "@/presentation/components/ui";
import { VoucherCard } from "@/presentation/components/voucherCard";

interface VoucherItem {
    id: string;
    bgColor: string;
    title: string;
    value: string;

    isBlack?: boolean;
    isGreen?: boolean;
}

const MyVoucherScreen = () => {
    const vouchers: VoucherItem[] = [
        { id: "1", bgColor: "#E91E63", title: "foodpanda", value: "$0.5" },
        { id: "2", bgColor: "#4CAF50", title: "GrabRewards", value: "550 Grab Points" },
        { id: "3", bgColor: "#FFFFFF", title: "stojo", value: "10%", isBlack: true },
        { id: "4", bgColor: "#000000", title: "anywheel", value: "20%", isGreen: true }
    ];

    const renderVoucherItem: ListRenderItem<VoucherItem> = ({ item }) => (
        <VoucherCard
            bgColor={item.bgColor}
            title={item.title}
            value={item.value}
            points="Use"
            isBlack={item.isBlack}
            isGreen={item.isGreen}
            variant="voucher"
        />
    );

    const renderEmptyState = React.useMemo(() => {
        return (
            <EmptyState
                title="You haven’t redeemed any voucher"
                description="Browse available vouchers in Rewards."
                buttonText="Redeem my points"
            />
        );
    }, []);

    return (
        <Container>
            <Header title="My Voucher" />

            <Box className="mx-3 flex-1">
                <ListView
                    keyList="id"
                    data={vouchers}
                    renderItem={renderVoucherItem}
                    numColumns={2}
                    emptyComponent={renderEmptyState}
                />
            </Box>
        </Container>
    );
};

export default MyVoucherScreen;
