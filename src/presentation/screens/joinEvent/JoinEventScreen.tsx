import React, { useState } from "react";

import { Header } from "@/presentation/components/header";
import { Input } from "@/presentation/components/input";
import { MyButton } from "@/presentation/components/myButton";
import { Box, Container, Text, VStack } from "@/presentation/components/ui";
import { ImageAssets } from "@/shared/constants";

const JoinEventScreen = () => {
    const [eventCode, setEventCode] = useState<string>("");

    const handleJoinEvent = () => {
        // Handle join event logic here
    };

    return (
        <Container>
            <Header title="Join Event" rightComponent={ImageAssets.activityOff} />

            <VStack className="px-4 flex-1 justify-between">
                <VStack className="gap-8">
                    <Text className="text-darkGray text-[16px] mt-4">
                        Have a code? Enter it below to join your event. It only takes a moment to get started.
                    </Text>

                    <VStack className="gap-2 items-center gap-y-4">
                        <Text className="text-center text-xl font-medium text-darkGray">Enter Event Code</Text>

                        <Box className="w-2/3">
                            <Input
                                value={eventCode}
                                onChangeText={setEventCode}
                                placeholder=""
                                className="bg-gray-100 rounded-lg p-4 text-lg"
                                autoCapitalize="characters"
                            />
                        </Box>
                    </VStack>

                    <Text className="text-center text-darkGray text-[13px]">
                        Heads up! You can only be part of one private event at a time.
                    </Text>
                </VStack>

                <VStack className="mb-8">
                    <MyButton text="Join Event" onPress={handleJoinEvent} variant="primary" />
                </VStack>
            </VStack>
        </Container>
    );
};

export default JoinEventScreen;
