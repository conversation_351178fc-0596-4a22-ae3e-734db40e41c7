import React, { useState } from "react";

import { Header } from "@/presentation/components/header";
import { Input } from "@/presentation/components/input";
import { MyButton } from "@/presentation/components/myButton";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container, Image, ScrollView, Text } from "@/presentation/components/ui";
import IconComponent from "@/presentation/components/ui/icon";
import { ImageAssets } from "@/shared/constants";

const ContactScreen = () => {
    const [images, setImages] = useState<string[]>([]);

    const handleAddImage = () => {
        if (images.length < 5) {
            setImages([...images, ImageAssets.placeholderImage]);
        }
    };

    // const handleRemoveImage = (index: number) => {
    //     const newImages = [...images];
    //     newImages.splice(index, 1);
    //     setImages(newImages);
    // };
    return (
        <Container>
            <Header title="Contact Us" />
            <ScrollView className="px-4 pt-5">
                <Text>Have questions? We&apos;re just a message away</Text>
                <Box className="gap-4 py-4">
                    <Input title="Nature of query" placeholder="Select nature of query" type="dropdown" required />
                    <Input title="Name" placeholder="Name" required />
                    <Input title="Email" placeholder="Email" required type="email" />
                    <Input title="Phone number" placeholder="Phone number" required type="phone" />
                    <Input title="Address" placeholder="Address" required />
                    <Input title="Postal Code" placeholder="Postal Code" required />
                    <Input title="Message" placeholder="Message" required multiline height={100} paddingTop={10} />
                    <Text>Attachements (Max. 5)</Text>
                    <Box className="flex-row justify-between py-2 w-full">
                        {Array.from({ length: 5 }).map((_, index) => {
                            if (images[index]) {
                                return (
                                    <Box
                                        key={`image-${index}`}
                                        className="h-[56px] w-[56px] rounded-lg overflow-hidden border-2 border-dashed border-lightGray items-center justify-center">
                                        <Image
                                            className="w-[24px] h-[24px] "
                                            source={ImageAssets.placeholderImage}
                                            alt={`placeholder index ${index}`}
                                        />
                                    </Box>
                                );
                            }
                            if (index === images.length && images.length < 5) {
                                return (
                                    <MyTouchable
                                        key={`add-${index}`}
                                        onPress={handleAddImage}
                                        className="bg-lightBlue rounded-[5px]">
                                        <Box className="h-[56px] w-[56px] items-center justify-center bg-gray-50">
                                            <IconComponent name="plus" size={24} color="#888888" />
                                        </Box>
                                    </MyTouchable>
                                );
                            }
                        })}
                    </Box>
                </Box>
            </ScrollView>
            <Box className="p-4">
                <MyButton text="Submit" />
            </Box>
        </Container>
    );
};

export default ContactScreen;
