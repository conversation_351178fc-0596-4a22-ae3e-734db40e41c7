import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import React, { useEffect, useRef } from "react";
import RBSheet from "react-native-raw-bottom-sheet";

import { getColor } from "@/presentation/hooks";

import { FilterItem } from "./Filter";

import { BottomSheetCustom } from "@/presentation/components/bottomSheet";
import { Input } from "@/presentation/components/input";
import { MapComponent, MapMarker } from "@/presentation/components/map";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container, HStack, IconComponent, Image, ScrollView, Text, VStack } from "@/presentation/components/ui";
import { RouteName } from "@/shared/constants";
import ImageAssets from "@/shared/constants/ImageAssets";

type SearchScreenRouteProp = RouteProp<
    {
        Search: {
            filters?: FilterItem[];
        };
    },
    "Search"
>;

// Mock data for search results
const mockSearchResults = [
    {
        id: "1",
        type: "ICT, Bulbs, and Batteries",
        accepts: "Battery",
        location: "1 Harbourfront Walk, Vivocity, #2-07",
        distance: "0.06km",
        image: ImageAssets.ictBulbEwasteBins
    },
    {
        id: "2",
        type: "ICT, Bulbs, and Batteries",
        accepts: "Battery",
        location: "3 Temasek Boulevard, Suntec City, #B1-132",
        distance: "1.2km",
        image: ImageAssets.ictBulbEwasteBins
    },
    {
        id: "3",
        type: "ICT, Bulbs, and Batteries",
        accepts: "Battery",
        location: "313 Orchard Road, 313@Somerset, #B3-25",
        distance: "2.5km",
        image: ImageAssets.ictBulbEwasteBins
    },
    {
        id: "4",
        type: "ICT, Bulbs, and Batteries",
        accepts: "Battery",
        location: "68 Orchard Road, Plaza Singapura, #B2-23",
        distance: "3.1km",
        image: ImageAssets.ictBulbEwasteBins
    }
];

const SearchScreen = () => {
    const navigation = useNavigation();
    const route = useRoute<SearchScreenRouteProp>();
    const [searchQuery, setSearchQuery] = React.useState("");
    const [activeFilters, setActiveFilters] = React.useState<FilterItem[]>([]);
    const [searchResults, _setSearchResults] = React.useState(mockSearchResults);
    const bottomSheetRef = useRef<RBSheet>(null);

    useEffect(() => {
        if (route.params?.filters) {
            setActiveFilters(route.params.filters);
        }
    }, [route.params?.filters]);

    const handleBack = () => {
        navigation.goBack();
    };

    const handleFilterPress = () => {
        navigation.navigate(RouteName.Filter);
    };

    const handleNavigatePress = (_result: any) => {
        // Handle navigation to the selected bin
        // Implementation will be added later
    };

    const handleSearch = () => {
        if (searchQuery.trim()) {
            // In a real app, you would filter results based on searchQuery
            bottomSheetRef.current?.open();
        }
    };

    const handleSearchSubmit = () => {
        handleSearch();
    };

    const activeFilterCount = activeFilters.length;

    // Convert search results to map markers
    const markers: MapMarker[] = searchResults.map((result) => ({
        id: result.id,
        coordinate: [103.8198 + Math.random() * 0.02, 1.3521 + Math.random() * 0.02] as [number, number],
        title: result.type,
        description: result.location,
        iconImage: ImageAssets.icMapICT
    }));

    const handleMarkerPress = () => {
        bottomSheetRef.current?.open();
    };

    return (
        <Container>
            <Box>
                <HStack className="px-4 items-center justify-between">
                    <MyTouchable onPress={handleBack}>
                        <Box className="w-6 h-6 justify-center items-center">
                            <Image source={ImageAssets.icBack} alt="back" className="w-5 h-5" />
                        </Box>
                    </MyTouchable>

                    <Box className="flex-1 mx-3">
                        <HStack className="items-center border border-[#ECEEF1] rounded-xl px-4 py-3">
                            <Box className="w-[24px] h-[24px]">
                                <MyTouchable onPress={handleSearch}>
                                    <Image
                                        source={ImageAssets.icSearch}
                                        alt="search"
                                        className="w-full h-full"
                                        tintColor="#A0AEC0"
                                    />
                                </MyTouchable>
                            </Box>
                            <Box className="flex-1">
                                <Input
                                    placeholder="What do you want to recycle?"
                                    value={searchQuery}
                                    onChangeText={setSearchQuery}
                                    height={24}
                                    backgroundColor="transparent"
                                    onSubmitEditing={handleSearchSubmit}
                                    returnKeyType="search"
                                />
                            </Box>
                        </HStack>
                    </Box>

                    <MyTouchable onPress={handleFilterPress}>
                        <Box className="w-6 h-6 justify-center items-center">
                            <HStack className="relative">
                                <IconComponent name="filter-outline" font="ionicons" size={24} color="#1A202C" />
                                {activeFilterCount > 0 && (
                                    <Box className="absolute -top-1 -right-1 bg-green-500 rounded-full w-4 h-4 items-center justify-center">
                                        <Text className="text-white text-[10px] font-bold">{activeFilterCount}</Text>
                                    </Box>
                                )}
                            </HStack>
                        </Box>
                    </MyTouchable>
                </HStack>
            </Box>

            {activeFilterCount > 0 && (
                <Box className="px-4 py-2">
                    <HStack className="flex-wrap gap-2">
                        {activeFilters.map((filter) => (
                            <Box key={filter.id} className="bg-grayCard py-1 px-3 rounded-full">
                                <Text className="text-xs font-medium">{filter.label}</Text>
                            </Box>
                        ))}
                    </HStack>
                </Box>
            )}

            <Box className="flex-1 bg-white">
                <Box className="flex-1 bg-gray-100 mt-4">
                    <MapComponent markers={markers} onMarkerPress={handleMarkerPress} />
                </Box>
            </Box>

            {/* Bottom Sheet for search results */}
            <BottomSheetCustom
                ref={bottomSheetRef}
                height={562}
                closeOnDragDown={true}
                closeOnPressMask={true}
                draggableIconColor="#E0E6ED">
                <ScrollView>
                    <VStack className="px-4 pb-10">
                        <Text className="text-darkGray text-[15px] mt-4">
                            Use these bins to sort and dispose of recyclables correctly.
                        </Text>

                        <VStack className="mt-4">
                            <VStack>
                                <Text className="font-bold text-[16px] text-blackLight">ICT, Bulbs, and Batteries</Text>
                                <Text className="text-[15px] text-darkGray">Accepts: Battery</Text>
                            </VStack>
                        </VStack>

                        {searchResults.map((result, index) => (
                            <React.Fragment key={result.id}>
                                {index > 0 && <Box className="h-[1px] bg-lightGray my-3" />}
                                <HStack className="bg-white rounded-xl p-2 mt-3">
                                    <Box className="w-[56px] h-[56px] rounded-lg overflow-hidden bg-lightBlue justify-center items-center border-2 border-white shadow-md">
                                        <Image source={result.image} alt={result.type} className="w-[32px] h-[32px]" />
                                    </Box>
                                    <VStack className="flex-1 ml-2">
                                        <HStack className="items-center gap-2">
                                            <Box className="w-4 h-4">
                                                <Image
                                                    source={ImageAssets.tablerWalk}
                                                    alt="distance"
                                                    className="w-full h-full"
                                                    tintColor={getColor("green")}
                                                />
                                            </Box>
                                            <Text className="text-[16px] text-green">{result.distance}</Text>
                                        </HStack>
                                        <HStack className="gap-2">
                                            <Box>
                                                <IconComponent
                                                    name="map-pin"
                                                    font="feather"
                                                    size={16}
                                                    color={getColor("neutralGray")}
                                                />
                                            </Box>
                                            <Box className="flex-1">
                                                <Text className="text-[15px] text-blackLight flex-1">
                                                    {result.location}
                                                </Text>
                                            </Box>
                                        </HStack>
                                    </VStack>
                                    <MyTouchable onPress={() => handleNavigatePress(result)}>
                                        <Box className="w-[36px] h-[36px] rounded-full bg-lightBlue justify-center items-center">
                                            <Box className="w-[19px] h-[19px]">
                                                <Image
                                                    source={ImageAssets.icNavigation}
                                                    alt="navigation"
                                                    className="w-full h-full"
                                                />
                                            </Box>
                                        </Box>
                                    </MyTouchable>
                                </HStack>
                            </React.Fragment>
                        ))}
                    </VStack>
                </ScrollView>
            </BottomSheetCustom>
        </Container>
    );
};

export default SearchScreen;
