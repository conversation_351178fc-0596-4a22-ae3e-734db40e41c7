import { useNavigation } from "@react-navigation/native";
import React, { useState } from "react";
import { ScrollView } from "react-native";

import { Header } from "@/presentation/components/header";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container, HStack, Text } from "@/presentation/components/ui";

export interface FilterItem {
    id: string;
    label: string;
    checked: boolean;
}

const Filter = () => {
    const navigation = useNavigation();
    const [filterItems, setFilterItems] = useState<FilterItem[]>([
        { id: "printer", label: "Printer", checked: true },
        { id: "computer", label: "Computer & Laptop", checked: true },
        { id: "mobile", label: "Mobile & Tablet", checked: false },
        { id: "network", label: "Network & Set-top Box", checked: false },
        { id: "tv", label: "TV & Desktop Monitor", checked: false },
        { id: "bulb", label: "Bulb", checked: false },
        { id: "batteries", label: "Batteries", checked: false },
        { id: "water_bottle", label: "Empty Water Bottle", checked: false },
        { id: "aluminium_can", label: "Empty Aluminium Can", checked: false },
        { id: "paper", label: "Paper", checked: true },
        { id: "metal", label: "Metal", checked: true },
        { id: "plastic", label: "Plastic", checked: false },
        { id: "glass", label: "Glass", checked: false }
    ]);

    const handleResetFilter = () => {
        setFilterItems(filterItems.map((item) => ({ ...item, checked: false })));
    };

    const handleToggleItem = (id: string) => {
        setFilterItems(filterItems.map((item) => (item.id === id ? { ...item, checked: !item.checked } : item)));
    };

    const handleBack = () => {
        navigation.goBack();
    };

    // const handleApplyFilters = () => {
    //     // Get selected filters
    //     const selectedFilters = filterItems.filter((item) => item.checked);

    //     // Navigate back to search screen with selected filters
    //     navigation.navigate("Search", {
    //         filters: selectedFilters
    //     });
    // };

    const renderFilterItem = (item: FilterItem) => (
        <HStack key={item.id} className="justify-between items-center py-1 px-4">
            <Box className="bg-[#F7F7F7] py-2 px-4 rounded-xl">
                <Text className="text-[13px] font-semibold text-[#1A202C]">{item.label}</Text>
            </Box>
            <MyTouchable onPress={() => handleToggleItem(item.id)}>
                <Box
                    className={`w-6 h-6 rounded border-2 items-center justify-center ${
                        item.checked ? "bg-[#44A12B] border-[#44A12B]" : "border-[#44A12B]"
                    }`}>
                    {item.checked && <Text className="text-white font-bold">✓</Text>}
                </Box>
            </MyTouchable>
        </HStack>
    );

    // const selectedCount = filterItems.filter((item) => item.checked).length;

    return (
        <Container>
            <Header
                title="Filter"
                rightComponent={
                    <MyTouchable onPress={handleResetFilter}>
                        <Text className="text-darkBlue text-base">Reset filter</Text>
                    </MyTouchable>
                }
                onPress={handleBack}
            />
            <ScrollView className="flex-1 pt-4">
                <Box className="pb-6">{filterItems.map(renderFilterItem)}</Box>
            </ScrollView>

            {/* <VStack className="p-4">
                <MyButton text={`Apply Filters (${selectedCount})`} variant="primary" onPress={handleApplyFilters} />
            </VStack> */}
        </Container>
    );
};

export default Filter;
