import { useNavigation } from "@react-navigation/native";
import React from "react";

import { useLogoutQueries, useUserQueries } from "@/data/queries";

import { AccountName } from "@/presentation/components/account";
import { BoxCard } from "@/presentation/components/boxCard";
import { <PERSON><PERSON> } from "@/presentation/components/header";
import { MenuItem } from "@/presentation/components/item";
import { MyButton } from "@/presentation/components/myButton";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container, HStack, Image, ScrollView, Text, VStack } from "@/presentation/components/ui";
import { usePoint } from "@/presentation/hooks/point";
import { ImageAssets, RouteName } from "@/shared/constants";

const AccountScreen: React.FC = () => {
    const navigation = useNavigation();
    const { isLoading: isLoadingUser, user } = useUserQueries();
    const { point } = usePoint();

    const { logout, isLoading } = useLogoutQueries();

    const isLoadingPage = React.useMemo(() => isLoadingUser || isLoading, [isLoading, isLoadingUser]);

    const MENU_SECTIONS = React.useMemo(
        () => [
            {
                title: "ACTIVITY",
                items: [
                    { icon: ImageAssets.icCoins, title: "CO2 Points", onPress: () => {} },
                    { icon: ImageAssets.icTicketCheck, title: "Redeem Rewards", onPress: () => {} },
                    {
                        icon: ImageAssets.icCalendar,
                        title: "Events & Services",
                        onPress: () => navigation.navigate(RouteName.JoinEvent)
                    },
                    { icon: ImageAssets.icFile, title: "Resources", onPress: () => {} }
                ]
            },
            {
                title: "SETTINGS",
                items: [{ icon: ImageAssets.icLanguages, title: "Language", onPress: () => {} }]
            },
            {
                title: "OTHERS",
                items: [
                    {
                        icon: ImageAssets.icHelp,
                        title: "Contact Us",
                        onPress: () => navigation.navigate(RouteName.Contact)
                    },
                    { icon: ImageAssets.icStar, title: "Ratings", onPress: () => {} },
                    {
                        icon: ImageAssets.icLogout,
                        title: "Log Out",
                        onPress: logout
                    }
                ]
            }
        ],
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    const handleEditAccount = React.useCallback(() => {
        navigation.navigate(RouteName.EditAccount);
    }, [navigation]);

    const handleRedeemPress = React.useCallback(() => {
        navigation.navigate(RouteName.RedeemPoint);
    }, [navigation]);

    const renderPointsCard = React.useMemo(
        () => (
            <BoxCard>
                <Box className="p-5 gap-y-4">
                    <HStack className="items-center justify-between">
                        <VStack>
                            <Text className="text-base mb-1 text-white">
                                CO<Text className="text-xs align-top text-white">2</Text> Points
                            </Text>
                            <HStack className="items-center gap-x-2">
                                <Image source={ImageAssets.icPoint} className="w-[20px] h-[20px]" alt="Point" />
                                <Text className="text-2xl font-bold text-white">{point}</Text>
                            </HStack>
                        </VStack>
                        <MyButton text="Redeem" width={110} height={44} onPress={handleRedeemPress} variant="outline" />
                    </HStack>
                    <Image source={ImageAssets.lineWhite} className="h-[2px] w-full" resizeMode="cover" alt="Line" />

                    <Box className="bg-green-100">
                        <Text className="text-center text-sm text-white">
                            1 CO<Text className="text-xs align-top text-white">2</Text> point equals to value
                        </Text>
                    </Box>
                </Box>
            </BoxCard>
        ),
        [handleRedeemPress, point]
    );

    const renderProfileHeader = React.useMemo(
        () => (
            <>
                <Box className="px-5 h-[80px] items-center justify-center">
                    <HStack className="items-center justify-between flex-1">
                        <AccountName user={user} isLoading={isLoadingUser} />
                        <MyTouchable onPress={handleEditAccount}>
                            <Text className="text-[16px] text-darkBlue">Edit Account</Text>
                        </MyTouchable>
                    </HStack>
                </Box>
                {renderPointsCard}
            </>
        ),
        [handleEditAccount, isLoadingUser, user, renderPointsCard]
    );

    const renderMenuSection = (section: (typeof MENU_SECTIONS)[0], index: number) => (
        <Box className="px-5 mb-6 gap-y-3" key={`section-${index}`}>
            <Text className="text-grayText mb-2">{section.title}</Text>
            <VStack className="divide-y divide-gray-100 bg-background-light rounded-[24px] p-5 gap-y-10">
                {section.items.map((item, idx) => (
                    <MenuItem
                        key={`${section.title}-${idx}`}
                        icon={item.icon}
                        title={item.title}
                        onPress={item.onPress}
                    />
                ))}
            </VStack>
        </Box>
    );

    const onNotificationPress = React.useCallback(() => {
        navigation.navigate(RouteName.Notification);
    }, [navigation]);

    return (
        <Container isLoading={isLoadingPage}>
            <Header title="Account" rightComponent={ImageAssets.icBell} onPress={onNotificationPress} />

            <Box className="bg-lightBlue flex-1">
                {renderProfileHeader}
                <ScrollView className="rounded-t-[16px] pt-5 overflow-hidden bg-white">
                    {MENU_SECTIONS.map(renderMenuSection)}
                </ScrollView>
            </Box>
        </Container>
    );
};

export default AccountScreen;
