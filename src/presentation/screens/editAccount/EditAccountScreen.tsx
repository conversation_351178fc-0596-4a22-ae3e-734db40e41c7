import { useNavigation } from "@react-navigation/native";
import React from "react";
import { Keyboard } from "react-native";
import { object } from "yup";

import { useUserStore } from "@/app/store";

import { useUpdateUserQueries } from "@/data/queries";

import { useForm, usePostalCodeValidation } from "@/presentation/hooks";

import { ShowBottomSheetObs } from "@/data/services/observable";
import { Header } from "@/presentation/components/header";
import { Input } from "@/presentation/components/input";
import { MyButton } from "@/presentation/components/myButton";
import { Box, Container, ScrollView, Text } from "@/presentation/components/ui";
import { usePoint } from "@/presentation/hooks/point";
import RouteName from "@/shared/constants/RouteName";
import { compareValue, formatDate } from "@/shared/helper";
import { addressSchema, dobSchema, firstNameSchema, lastNameSchema, postalCodeSchema } from "@/shared/validations";

const titleField = {
    firstName: "First Name",
    lastName: "Last Name",
    address: "Address",
    dateOfBirth: "Date of Birth",
    postalCode: "Postal Code"
};

const placeHolderField = {
    firstName: "First Name",
    lastName: "Last Name",
    address: "Address",
    dateOfBirth: "Date of Birth",
    postalCode: "Postal Code"
};

const EditAccountScreen: React.FC = () => {
    const { user } = useUserStore();
    const { point } = usePoint();
    const { updateUser, isLoading: isLoadingUpdateUser } = useUpdateUserQueries();
    const { isValidating, handlePostalCodeChange } = usePostalCodeValidation();

    const navigation = useNavigation();

    const initialValues = React.useMemo(() => {
        return {
            firstName: user?.first_name || "",
            lastName: user?.last_name || "",
            address: user?.address || "",
            dateOfBirth: user?.dob ? formatDate(user?.dob, "YYYY-MM-DD") : "",
            postalCode: user?.postal_code || ""
        };
    }, [user]);

    const { getInputProps, handleSubmit, formik, setFieldTouched, validateForm } = useForm({
        initialValues,
        validationSchema: object().shape({
            firstName: firstNameSchema,
            lastName: lastNameSchema,
            address: addressSchema,
            dateOfBirth: dobSchema,
            postalCode: postalCodeSchema
        }),
        onSubmit: async (values) => {
            await updateUser({
                first_name: values.firstName,
                last_name: values.lastName,
                address: values.address,
                dob: values.dateOfBirth,
                postal_code: values.postalCode
            });
            Keyboard.dismiss();
        }
    });

    const handleConfirmDeleteAccount = React.useCallback(() => {
        navigation.navigate(RouteName.Auth, { screen: RouteName.Verification });
    }, [navigation]);

    const handleDeleteAccount = React.useCallback(() => {
        ShowBottomSheetObs.action({
            title: "Are you sure you want to delete the account?",
            message: `All of the ${point} CO2 points will be gone`,
            titleButtonConfirm: "Cancel",
            titleButtonCancel: "Delete Account",
            onConfirm: handleConfirmDeleteAccount,
            onCancel: () => {}
        });
    }, [handleConfirmDeleteAccount, point]);

    const handleChangeValue = React.useCallback(
        (field: string, value: string) => {
            if (field === "dateOfBirth") {
                return formik.setFieldValue(field, formatDate(value, "YYYY-MM-DD"));
            }
            formik.setFieldValue(field, value);
        },
        [formik]
    );

    const handlePostalCodeInputChange = React.useCallback(
        (field: string, value: string) => {
            const baseProps = getInputProps("postalCode" as keyof typeof initialValues);
            handlePostalCodeChange(field, value, baseProps.onChangeValue!, setFieldTouched, validateForm);
        },
        [getInputProps, handlePostalCodeChange, setFieldTouched, validateForm]
    );

    const renderFormField = React.useCallback(
        (fieldKey: keyof typeof initialValues) => {
            return (
                <Box className="mb-5">
                    <Input
                        title={titleField[fieldKey]}
                        required={true}
                        placeholder={placeHolderField[fieldKey]}
                        {...(fieldKey === "postalCode"
                            ? { onChangeValue: handlePostalCodeInputChange }
                            : getInputProps(fieldKey))}
                        type={fieldKey === "dateOfBirth" ? "date" : undefined}
                        onChangeValue={handleChangeValue}
                        keyboardType={fieldKey === "postalCode" ? "numeric" : undefined}
                        isLoading={fieldKey === "postalCode" && isValidating}
                    />
                </Box>
            );
        },
        [getInputProps, handlePostalCodeInputChange, handleChangeValue, isValidating]
    );

    const isChanged = React.useMemo(() => compareValue(initialValues, formik.values), [initialValues, formik.values]);

    const isLoadingPage = React.useMemo(() => isLoadingUpdateUser, [isLoadingUpdateUser]);

    return (
        <Container isLoading={isLoadingPage}>
            <Header
                title="Edit Account"
                rightComponent={
                    <Text className="text-darkBlue font-medium" onPress={handleDeleteAccount}>
                        Delete Account
                    </Text>
                }
            />
            <ScrollView className="flex-1 px-5 pt-4">
                {renderFormField("firstName")}
                {renderFormField("lastName")}
                {renderFormField("address")}
                {renderFormField("dateOfBirth")}
                {renderFormField("postalCode")}
            </ScrollView>
            <Box className="p-4">
                <MyButton text="Save Changes" onPress={handleSubmit} disabled={isChanged} />
            </Box>
        </Container>
    );
};

export default EditAccountScreen;
