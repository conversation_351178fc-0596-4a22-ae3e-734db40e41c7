import { useNavigation } from "@react-navigation/native";
import { ListRenderItem } from "@shopify/flash-list";
import React, { useState } from "react";

import { EmptyState } from "@/presentation/components/emptyState";
import { Header } from "@/presentation/components/header";
import { ListView } from "@/presentation/components/listView";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container, HStack, IconComponent, Image, Text } from "@/presentation/components/ui";
import { ImageAssets, RouteName } from "@/shared/constants";

type TabType = "current" | "upcoming";

type EventItem = {
    id: string;
    title: string;
    image: any;
    date: string;
    time: string;
    location: string;
};

const DUMMY_EVENTS: EventItem[] = [
    {
        id: "1",
        title: "International E-Waste Day Art Competition & Exhibition 2022",
        image: ImageAssets.eventImage,
        date: "29 Oct 2025",
        time: "4:00 PM - 10:00 PM",
        location: "302 Tiong Bahru Road, Tiong Bahru Plaza"
    },
    {
        id: "2",
        title: "International E-Waste Day Art Competition & Exhibition 2022",
        image: ImageAssets.eventImage,
        date: "29 Oct 2025",
        time: "4:00 PM - 10:00 PM",
        location: "302 Tiong Bahru Road, Tiong Bahru Plaza"
    }
];

type TabButtonProps = {
    title: string;
    isActive: boolean;
    onPress: () => void;
};

const TabButton: React.FC<TabButtonProps> = ({ title, isActive, onPress }) => (
    <Box className={`py-2 px-6 rounded-full ${isActive ? "bg-darkBlue" : "bg-transparent"}`} onTouchEnd={onPress}>
        <Text className={`font-medium ${isActive ? "text-white" : "text-darkGray"}`}>{title}</Text>
    </Box>
);

const EventCard: React.FC<{ event: EventItem; onPress?: () => void }> = ({ event, onPress }) => (
    <MyTouchable className="mx-5 mb-5 bg-grayCard rounded-xl overflow-hidden shadow-sm" onPress={onPress}>
        <Image source={event.image} alt={event.title} className="w-full h-[180px]" resizeMode="cover" />
        <Box className="p-4">
            <Text className="text-lg font-bold mb-3">{event.title}</Text>
            <HStack className="items-center mb-2">
                <IconComponent name="calendar" font="feather" size={16} color="#6B7280" />
                <Text className="text-darkGray ml-2">
                    {event.date}, {event.time}
                </Text>
            </HStack>
            <HStack className="items-center">
                <IconComponent name="map-pin" font="feather" size={16} color="#6B7280" />
                <Text className="text-darkGray ml-2">{event.location}</Text>
            </HStack>
        </Box>
    </MyTouchable>
);

const EventsScreen: React.FC = () => {
    const navigation = useNavigation();
    const [activeTab, setActiveTab] = useState<TabType>("current");

    const handleEventPress = React.useCallback(() => {
        navigation.navigate(RouteName.EventDetail);
    }, [navigation]);

    const renderEventItem: ListRenderItem<EventItem> = ({ item }) => (
        <EventCard event={item} onPress={handleEventPress} />
    );

    const renderEmptyState = React.useMemo(() => {
        return (
            <Box className="mt-10">
                <EmptyState title="No current event available" description="Stay tune of our events" />
            </Box>
        );
    }, []);

    return (
        <Container>
            <Header title="Events" isShowBack={false} />

            <HStack className="px-5 mb-4">
                <TabButton
                    title="Current Events"
                    isActive={activeTab === "current"}
                    onPress={() => setActiveTab("current")}
                />
                <TabButton
                    title="Upcoming Events"
                    isActive={activeTab === "upcoming"}
                    onPress={() => setActiveTab("upcoming")}
                />
            </HStack>

            <ListView
                data={DUMMY_EVENTS}
                keyList="id"
                renderItem={renderEventItem}
                estimatedItemSize={300}
                showsVerticalScrollIndicator={false}
                emptyComponent={renderEmptyState}
            />
        </Container>
    );
};

export default EventsScreen;
