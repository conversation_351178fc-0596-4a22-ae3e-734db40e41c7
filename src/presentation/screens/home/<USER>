import { useActionSheet } from "@expo/react-native-action-sheet";
import * as Linking from "expo-linking";
import React from "react";
import RBSheet from "react-native-raw-bottom-sheet";

import { getColor, useBinDetail } from "@/presentation/hooks";

import { BottomSheetCustom } from "@/presentation/components/bottomSheet";
import { MyButton } from "@/presentation/components/myButton";
import { Box, HStack, Image, LoadingBox, ScrollView, Text } from "@/presentation/components/ui";
import { ImageAssets } from "@/shared/constants";

interface BinPressProps {
    binId?: number;
}

const BinPress = React.forwardRef<RBSheet, BinPressProps>(({ binId }, ref) => {
    const { isLoading, getBinDetail } = useBinDetail();

    const [binInfo, setBinInfo] = React.useState<BinDetailResponse | undefined>(undefined);
    const { showActionSheetWithOptions } = useActionSheet();

    React.useEffect(() => {
        const initBinInfo = async () => {
            if (binId) {
                const binDetailEndpoint = `bin-management/get/${binId}`;
                const bin = await getBinDetail(binDetailEndpoint);
                setBinInfo(bin);
            }
        };
        initBinInfo();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [binId]);

    const handleGetDirections = React.useCallback(() => {
        const options = ["Open with Google Maps", "Open with Apple Maps", "Cancel"];
        const cancelButtonIndex = 2;

        const handleMapSelection = (buttonIndex?: number) => {
            if (buttonIndex === 0) {
                const url =
                    binInfo?.google_maps_url ||
                    `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(binInfo?.address || "")}`;
                Linking.openURL(url);
            } else if (buttonIndex === 1) {
                const url =
                    binInfo?.apple_maps_url || `http://maps.apple.com/?q=${encodeURIComponent(binInfo?.address || "")}`;
                Linking.openURL(url);
            }
        };

        showActionSheetWithOptions(
            {
                options,
                cancelButtonIndex,
                title: "Get Directions"
            },
            handleMapSelection
        );
    }, [showActionSheetWithOptions, binInfo]);

    return (
        <BottomSheetCustom ref={ref} height={500} closeOnDragDown>
            <ScrollView>
                <Box>
                    <Box className="items-center justify-center p-5">
                        <Box className="w-full h-[310px] overflow-hidden rounded-lg">
                            <Box className="relative h-[250px]">
                                <Image
                                    source={{ uri: binInfo?.type.image_url || "" }}
                                    style={{
                                        width: "100%",
                                        height: "100%"
                                    }}
                                    resizeMode="contain"
                                    alt="Recycle Bin"
                                />
                                <Box
                                    className="absolute bottom-[-40px] overflow-hidden left-0 right-0 items-center bg-grayCard h-[56px]"
                                    borderRadius={12}>
                                    <Box className="flex-1 items-center">
                                        <HStack className="items-center px-2 justify-center flex-1">
                                            <HStack className="flex-1 items-center">
                                                <Image
                                                    source={ImageAssets.icICT}
                                                    className="w-[32px] h-[32px]"
                                                    alt="ICT"
                                                />
                                                <Box className="px-2 flex-1">
                                                    <Text className="text-[13px]">{binInfo?.type.name}</Text>
                                                </Box>
                                            </HStack>
                                            <HStack className="items-center  bg-green px-2 py-2" borderRadius={8}>
                                                <Image
                                                    source={ImageAssets.tablerWalk}
                                                    className="w-[18px] h-[18px]"
                                                    alt="Walk"
                                                />
                                                <Text className="text-white text-[13px]">0.06km</Text>
                                            </HStack>
                                        </HStack>
                                    </Box>
                                </Box>
                            </Box>
                        </Box>
                    </Box>
                </Box>
            </ScrollView>
            <Box className="px-4">
                <HStack className="items-start gap-x-2 h-[50px]">
                    <Image
                        source={ImageAssets.homeOff}
                        className="w-[18px] h-[18px]"
                        tintColor={getColor("neutralGray")}
                        alt="Home"
                    />
                    <Box className="flex-1">
                        <Text> {isLoading ? "Loading..." : binInfo?.address}</Text>
                    </Box>
                </HStack>
                <MyButton text="Get Directions" className="mt-3 mb-3" height={44} onPress={handleGetDirections} />
            </Box>
            <LoadingBox isLoading={isLoading} />
        </BottomSheetCustom>
    );
});

export default BinPress;
