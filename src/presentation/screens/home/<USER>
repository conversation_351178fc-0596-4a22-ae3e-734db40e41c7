import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import React from "react";
import { ActivityIndicator } from "react-native";
import RBSheet from "react-native-raw-bottom-sheet";

import { getColor, useBin, useRouteParams } from "@/presentation/hooks";

import BinPress from "./BinPress";
import Filter from "./Filter";

import { walkthroughService } from "@/data/services/observable/WalkthroughObs";
import { Loading } from "@/presentation/components/loading";
import { ExtendedMapViewRef, MapComponent, MapInfoButton, MapMarker } from "@/presentation/components/map";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container, HStack, IconComponent, Image, Text } from "@/presentation/components/ui";
import { PointsWalkthrough } from "@/presentation/components/walkthrough";
import { usePoint } from "@/presentation/hooks/point";
import { useLocationServices } from "@/presentation/hooks/useLocationServices";
import { ImageAssets, RouteName } from "@/shared/constants";
import BinTypes from "@/shared/constants/BinTypes";

const HomeScreen = () => {
    const navigation = useNavigation();
    const params = useRouteParams<typeof RouteName.Home>();

    const mapRef = React.useRef<ExtendedMapViewRef>(null);
    const [isPointsWalkthroughVisible, setIsPointsWalkthroughVisible] = React.useState(false);
    const [binId, setBinId] = React.useState<number | undefined>(undefined);
    const [isScreenFocused, setIsScreenFocused] = React.useState(true);

    const bottomSheetRef = React.useRef<RBSheet>(null);

    const { goToUserLocation, isLoadingLocation, userLocation } = useLocationServices();
    const { bins, isLoading, binsByWasteTypeId, getBinsByWasteTypeId } = useBin();
    const { point } = usePoint();

    useFocusEffect(
        React.useCallback(() => {
            setIsScreenFocused(true);
            return () => {
                setIsScreenFocused(false);
            };
        }, [])
    );

    const markers = React.useMemo(() => {
        if (!bins || !isScreenFocused) return [];

        const binsToUse = binsByWasteTypeId || bins;

        return binsToUse.map((bin): MapMarker => {
            let iconImage = ImageAssets.icMapBatteries;

            const category = BinTypes.determineBinCategory(bin.bin_type_id, bin.e_waste_bin_type_id);

            if (bin.type.image_url) {
                iconImage = { uri: bin.type.image_url };
            }

            return {
                id: `bin_${bin.id}`,
                coordinate: [parseFloat(bin.long), parseFloat(bin.lat)] as [number, number],
                title: `${bin.type.name} - ${bin.code}`,
                description: bin.address,
                category,
                iconImage
            };
        });
    }, [bins, binsByWasteTypeId, isScreenFocused]);

    const handleAccountPress = React.useCallback(() => {
        navigation.navigate(RouteName.Account);
    }, [navigation]);

    const handleMyLocationPress = React.useCallback(() => {
        if (mapRef.current) {
            mapRef.current.enableUserLocationFollowing?.(true);
        }
        goToUserLocation(mapRef);
    }, [goToUserLocation]);

    const handleShowWalkthrough = React.useCallback(() => {
        walkthroughService.showWalkthrough(true);
    }, []);

    React.useEffect(() => {
        const initWalkthrough = async () => {
            if (params?.fromWalkthrough) {
                const timer = setTimeout(() => {
                    setIsPointsWalkthroughVisible(true);
                }, 500);
                return () => clearTimeout(timer);
            }
        };
        initWalkthrough();
    }, [params?.fromWalkthrough]);

    const handleMarkerPress = React.useCallback((_marker: MapMarker) => {
        const idString = _marker.id;
        const binIdMatch = idString.match(/bin_(\d+)/);
        if (binIdMatch && binIdMatch[1]) {
            const extractedBinId = Number(binIdMatch[1]);
            setBinId(extractedBinId);
            bottomSheetRef.current?.open();
        }
    }, []);

    return (
        <>
            <Box className="z-10 bg-white pt-safe" />
            <Container safeArea={false}>
                <Box className="flex-1 items-center justify-center">
                    {isScreenFocused && (
                        <MapComponent
                            ref={mapRef}
                            markers={markers}
                            onMarkerPress={handleMarkerPress}
                            userLocation={userLocation}
                        />
                    )}
                    <Loading isLoading={isLoading} />

                    <MapInfoButton />

                    <Box className="absolute top-[10px] right-[10px] left-[10px] z-10">
                        <HStack className="items-center justify-between w-full">
                            <LinearGradient
                                colors={[getColor("blue"), getColor("green")]}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 1, y: 0 }}
                                style={{ borderRadius: 999, padding: 2 }}>
                                <MyTouchable
                                    onPress={handleShowWalkthrough}
                                    className="px-3 py-1 flex-row items-center justify-center bg-transparent"
                                    style={{ borderRadius: 999 }}>
                                    <Image
                                        source={ImageAssets.icActivity}
                                        className="w-[24px] h-[24px]"
                                        alt="activity"
                                        tintColor="white"
                                    />
                                    <Text className="text-white font-bold ml-2">Recyling Tips</Text>
                                </MyTouchable>
                            </LinearGradient>
                            <HStack className="items-center gap-x-3">
                                <PointsWalkthrough
                                    isVisible={isPointsWalkthroughVisible}
                                    onClose={() => setIsPointsWalkthroughVisible(false)}
                                    currentStep={4}
                                    totalSteps={4}>
                                    <HStack
                                        alignItems="center"
                                        className="gap-x-2 bg-white rounded-full px-4 h-[36px] shadow-md">
                                        <Image source={ImageAssets.icPoint} className="w-[15px] h-[15px]" alt="Point" />
                                        <Text className="text-[13px]">{point}</Text>
                                    </HStack>
                                </PointsWalkthrough>
                                <MyTouchable
                                    onPress={handleAccountPress}
                                    className="bg-white rounded-full w-[36px] h-[36px] shadow-md">
                                    <Box className="flex-1 items-center justify-center">
                                        <IconComponent
                                            name="user"
                                            font="feather"
                                            size={20}
                                            color={getColor("darkGray")}
                                        />
                                    </Box>
                                </MyTouchable>
                            </HStack>
                        </HStack>
                    </Box>

                    <MyTouchable
                        className="absolute right-5 bottom-[35px] bg-white p-2 rounded-full shadow-md z-10"
                        onPress={handleMyLocationPress}
                        disabled={isLoadingLocation}>
                        {isLoadingLocation ? (
                            <ActivityIndicator size="small" color={getColor("green")} />
                        ) : (
                            <Image source={ImageAssets.icLocation} className="w-[16px] h-[16px]" alt="location" />
                        )}
                    </MyTouchable>
                </Box>
                <Filter onPress={(id) => getBinsByWasteTypeId(id)} />
            </Container>

            <BinPress ref={bottomSheetRef} binId={binId} />
        </>
    );
};

export default HomeScreen;
