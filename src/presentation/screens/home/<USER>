import { useNavigation } from "@react-navigation/native";
import React, { useMemo, useState } from "react";
import { ActivityIndicator } from "react-native";

import { useRecyclables } from "@/presentation/hooks";

import { Chip } from "@/presentation/components/chip";
import { Notch } from "@/presentation/components/notch";
import { Box, ScrollView, Text } from "@/presentation/components/ui";
import { ImageAssets, RouteName } from "@/shared/constants";
import { IconName } from "@/shared/types/icon";

interface FilterChip {
    id: string;
    label: string;
    icon?: string | number;
    isSearch?: boolean;
    category?: string;
    onPress?: () => void;
}

interface FilterProps {
    onPress?: (id: number) => void;
}

const Filter: React.FC<FilterProps> = ({ onPress }) => {
    const { recyclables, isLoading } = useRecyclables();
    const navigation = useNavigation();
    const [isShowingMore, setIsShowingMore] = useState(false);

    const handleSearchPress = React.useCallback(() => {
        navigation.navigate(RouteName.Search);
    }, [navigation]);

    const handleMorePress = React.useCallback(() => {
        setIsShowingMore((prev) => !prev);
    }, []);

    const handleChipPress = React.useCallback(
        (id: number) => {
            onPress?.(id);
        },
        [onPress]
    );

    const filterChips = useMemo(() => {
        const chips: FilterChip[] = [
            {
                id: "search",
                label: "Search",
                icon: ImageAssets.icSearch,
                isSearch: true,
                onPress: handleSearchPress
            }
        ];

        if (recyclables && recyclables.length > 0) {
            const itemsToShow = isShowingMore ? recyclables : recyclables.slice(0, 3);

            itemsToShow.forEach((item) => {
                chips.push({
                    id: String(item.id),
                    label: item.name,
                    category: item.name,
                    onPress: () => handleChipPress(item.id)
                });
            });
        }

        if (recyclables && recyclables.length > 3) {
            chips.push({
                id: "more",
                label: isShowingMore ? "Less" : "More",
                icon: isShowingMore ? "chevron-up" : ("more-horizontal" as IconName),
                onPress: handleMorePress
            });
        }

        return chips;
    }, [recyclables, isShowingMore, handleSearchPress, handleChipPress, handleMorePress]);

    return (
        <Box className="bg-white rounded-t-[16px] overflow-hidden mt-[-30px] relative z-20 min-h-[110px] shadow-lg">
            <Notch />

            <Box className="px-4 flex-1">
                <Text className="text-base font-bold mb-3">Ready to recycle? Let&apos;s locate the point</Text>

                <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    className="flex-row"
                    contentContainerStyle={{ gap: 8 }}>
                    {filterChips.map((chip) => (
                        <Chip key={chip.id} icon={chip.icon} label={chip.label} onPress={chip.onPress} />
                    ))}
                </ScrollView>
            </Box>
            {isLoading && (
                <Box className="absolute bottom-0 left-0 right-0 top-0 flex justify-center items-center">
                    <ActivityIndicator size="small" color="black" />
                </Box>
            )}
        </Box>
    );
};

export default Filter;
