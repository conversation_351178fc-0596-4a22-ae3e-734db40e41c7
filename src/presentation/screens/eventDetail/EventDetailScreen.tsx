import React from "react";

import { Header } from "@/presentation/components/header";
import { MyButton } from "@/presentation/components/myButton";
import { Box, Container, HStack, IconComponent, Image, ScrollView, Text, VStack } from "@/presentation/components/ui";
import { ImageAssets } from "@/shared/constants";

const EventDetailScreen: React.FC = () => {
    const eventDetail = {
        id: "1",
        title: "International E-Waste Day Art Competition & Exhibition 2022",
        image: ImageAssets.eventImage,
        date: "29 Oct 2025",
        time: "4:00 PM - 10:00 PM",
        location: "302 Tiong Bahru Road, Tiong Bahru Plaza",
        description:
            "The themes are relevant to the issue of e waste and are designed to encourage students to research and understand the issue on a deeper level as they interpret the themes and express their learnings through art. Over 60 submissions have been received, and 26 were shortlisted for the exhibition."
    };

    const handleRegisterEvent = () => {};

    const handleOpenMap = () => {};

    return (
        <Container>
            <Header title="Event" />

            <ScrollView className="flex-1">
                <Image
                    source={eventDetail.image}
                    alt={eventDetail.title}
                    className="w-full h-[200px]"
                    resizeMode="cover"
                />

                <Box className="px-5 py-4">
                    <Text className="text-xl font-bold">{eventDetail.title}</Text>
                </Box>

                <Box className="mx-5 mb-4 bg-gray rounded-xl p-4">
                    <VStack space="md">
                        <Box>
                            <Text className="text-neutralGray">Date, Time</Text>
                            <HStack className="items-center mt-1">
                                <IconComponent name="calendar" font="feather" size={16} color="#6B7280" />
                                <Text className="ml-2 text-darkGray">
                                    {eventDetail.date}, {eventDetail.time}
                                </Text>
                            </HStack>
                        </Box>

                        <Box>
                            <Text className="text-neutralGray">Location</Text>
                            <HStack className="items-center mt-1">
                                <IconComponent name="map-pin" font="feather" size={16} color="#6B7280" />
                                <Text className="ml-2 text-darkGray">{eventDetail.location}</Text>
                            </HStack>
                        </Box>
                    </VStack>
                </Box>

                <Box className="px-5 mb-6">
                    <Text className="text-darkGray leading-6">{eventDetail.description}</Text>
                </Box>
            </ScrollView>

            <VStack className="px-5 pt-5" space="md">
                <MyButton text="Register Event" onPress={handleRegisterEvent} variant="primary" />
                <MyButton text="Open Map" onPress={handleOpenMap} variant="secondary" />
            </VStack>
        </Container>
    );
};

export default EventDetailScreen;
