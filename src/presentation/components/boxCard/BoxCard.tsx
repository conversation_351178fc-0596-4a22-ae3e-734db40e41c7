import React from "react";
import { ImageBackground } from "react-native";

import { Box } from "../ui";

import { ImageAssets } from "@/shared/constants";

type BoxCardProps = {
    children: React.ReactNode;
};

const BoxCard: React.FC<BoxCardProps> = ({ children }) => {
    return (
        <Box className="mx-5 mb-6">
            <Box className="rounded-xl overflow-hidden">
                <ImageBackground source={ImageAssets.accentBackground} resizeMode="cover" alt="Accent Background">
                    {children}
                </ImageBackground>
            </Box>
        </Box>
    );
};

export default BoxCard;
