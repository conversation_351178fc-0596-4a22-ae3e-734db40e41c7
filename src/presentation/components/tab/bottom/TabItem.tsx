import { NavigationState, PartialState, Route, useNavigation } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import React from "react";
import { runOnJS } from "react-native-reanimated";

import { getColor } from "@/presentation/hooks";

import { MyTouchable } from "../../touchable";
import { Box, Image, Text } from "../../ui";
import FloatingButtonWalkthrough from "../../walkthrough/FloatingButtonWalkthrough";

import { walkthroughService } from "@/data/services/observable/WalkthroughObs";
import { ImageAssets, RouteName } from "@/shared/constants";
import { isFirstAppLaunch } from "@/shared/helper";

// Module-level variable to track if the walkthrough has been shown

type RouteWithState = Route<string> & {
    state?: NavigationState | PartialState<NavigationState> | undefined;
};

type TabItemProps = {
    route: RouteWithState;
    onPress: (index: number, routeName: keyof typeof RouteName, route: RouteWithState) => void;
    index: number;
    indexSelect: number;
    isFloating?: boolean;
};

type TabIconData = {
    title: string;
    iconOn: any;
    iconOff: any;
};

const TAB_DATA: TabIconData[] = [
    { title: "Home", iconOn: ImageAssets.homeOn, iconOff: ImageAssets.homeOff },
    { title: "Rewards", iconOn: ImageAssets.rewardOn, iconOff: ImageAssets.rewardOff },
    { title: "Scan", iconOn: ImageAssets.scan, iconOff: ImageAssets.scan },
    { title: "Event", iconOn: ImageAssets.eventOn, iconOff: ImageAssets.eventOff },
    { title: "Activity", iconOn: ImageAssets.activityOn, iconOff: ImageAssets.activityOff }
];

const FloatingButton: React.FC<{ onPress: () => void }> = ({ onPress }) => (
    <MyTouchable onPress={onPress} className="top-[-15px] bg-white rounded-full w-[60px] h-[60px]">
        <Box className="flex-1 items-center justify-center">
            <LinearGradient
                colors={[getColor("green"), getColor("blue")]}
                start={{ x: 0, y: 0.05 }}
                end={{ x: 1, y: 1.07 }}
                style={{
                    borderRadius: 31,
                    width: 50,
                    height: 50,
                    justifyContent: "center",
                    alignItems: "center"
                }}>
                <Image source={ImageAssets.scanLine} alt="Scan" className="w-[24px] h-[24px]" />
            </LinearGradient>
        </Box>
    </MyTouchable>
);

const RegularTabItem: React.FC<{
    tabData: TabIconData;
    isSelected: boolean;
    onTabPress: () => void;
}> = ({ tabData, isSelected, onTabPress }) => {
    const { title, iconOn, iconOff } = tabData;
    const isScanTab = title === "Scan";
    return (
        <MyTouchable alignItems="center" onPress={onTabPress} flex={1}>
            <Box className="items-center gap-2">
                <Image source={isSelected ? iconOn : iconOff} alt={title} className="w-[24px] h-[24px]" />
                {!isScanTab && (
                    <Text className={`${isSelected ? "text-green" : "text-darkGray"} text-[12px]`}>{title}</Text>
                )}
            </Box>
        </MyTouchable>
    );
};

let hasShownFloatingButtonWalkthrough = false;

const TabItem: React.FC<TabItemProps> = ({ route, onPress, index, indexSelect, isFloating }) => {
    const navigation = useNavigation();
    const [isWalkthroughVisible, setIsWalkthroughVisible] = React.useState(false);

    React.useEffect(() => {
        const initWalkthrough = async () => {
            const isFirstLaunch = await isFirstAppLaunch();
            if (isFirstLaunch) return;
            if (isFloating && !hasShownFloatingButtonWalkthrough) {
                walkthroughService.showWalkthrough(true);
                hasShownFloatingButtonWalkthrough = true;
            }
        };
        initWalkthrough();
    }, [isFloating]);

    React.useEffect(() => {
        const subscription = walkthroughService.walkthrough$.subscribe((event) => {
            if (event.show) {
                setIsWalkthroughVisible(event.show);
                if (event.show) {
                    hasShownFloatingButtonWalkthrough = true;
                }
            }
        });

        return () => {
            subscription.unsubscribe();
        };
    }, [isFloating]);

    const onTabPress = React.useCallback(() => {
        if (isFloating) {
            navigation.navigate(RouteName.ScanStack, { screen: RouteName.Scan });
            return;
        }
        onPress(index, route.name as keyof typeof RouteName, route);
    }, [index, onPress, route, isFloating, navigation]);

    const handleCloseWalkthrough = React.useCallback(() => {
        "worklet";
        runOnJS(setIsWalkthroughVisible)(false);
    }, []);

    const { title, iconOn, iconOff } = TAB_DATA[index];
    const isSelected = indexSelect === index;

    return (
        <>
            {isFloating ? (
                <FloatingButtonWalkthrough
                    isVisible={isWalkthroughVisible}
                    onClose={handleCloseWalkthrough}
                    title="Start recycling with us at different outlets"
                    content="Your actions matter. Find a spot and get started at your convenience"
                    currentStep={1}
                    totalSteps={4}>
                    <FloatingButton onPress={onTabPress} />
                </FloatingButtonWalkthrough>
            ) : (
                <RegularTabItem tabData={{ title, iconOn, iconOff }} isSelected={isSelected} onTabPress={onTabPress} />
            )}
        </>
    );
};

export default TabItem;
