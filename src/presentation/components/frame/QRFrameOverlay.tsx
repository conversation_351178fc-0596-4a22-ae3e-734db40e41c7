import React from "react";
import Svg, { Defs, Mask, Rect } from "react-native-svg";

import { Box } from "../ui";

import { fullHeight, fullWidth } from "@/shared/helper";

export const FRAME_SIZE = 280;
export const FRAME_BORDER_RADIUS = 24;
export const FRAME_CORNER_SIZE = 60;
export const FRAME_CORNER_THICKNESS = 7;

export const getFramePosition = () => {
    const centerX = fullWidth / 2;
    const centerY = fullHeight / 2;
    const frameLeft = centerX - FRAME_SIZE / 2;
    const frameTop = centerY - FRAME_SIZE / 2 - 100;

    return {
        left: frameLeft,
        top: frameTop,
        width: FRAME_SIZE,
        height: FRAME_SIZE
    };
};

const QRFrameOverlay = () => {
    const framePosition = getFramePosition();

    return (
        <Box className="absolute top-0 left-0 right-0 bottom-0">
            <Svg height={fullHeight} width={fullWidth}>
                <Defs>
                    <Mask id="mask" x="0" y="0" height="100%" width="100%">
                        <Rect height="100%" width="100%" fill="white" />

                        <Rect
                            x={framePosition.left}
                            y={framePosition.top}
                            width={framePosition.width}
                            height={framePosition.height}
                            rx={FRAME_BORDER_RADIUS}
                            ry={FRAME_BORDER_RADIUS}
                            fill="black"
                        />
                    </Mask>
                </Defs>

                <Rect height="100%" width="100%" fill="rgba(0, 0, 0, 0.6)" mask="url(#mask)" />
            </Svg>

            <Box
                position="absolute"
                top={framePosition.top}
                left={framePosition.left}
                width={framePosition.width}
                height={framePosition.height}
                backgroundColor="transparent">
                <Box
                    position="absolute"
                    top={0}
                    left={0}
                    width={FRAME_CORNER_SIZE}
                    height={FRAME_CORNER_SIZE}
                    borderTopWidth={FRAME_CORNER_THICKNESS}
                    borderLeftWidth={FRAME_CORNER_THICKNESS}
                    borderColor="white"
                    borderTopLeftRadius={FRAME_BORDER_RADIUS}
                />

                <Box
                    position="absolute"
                    top={0}
                    right={0}
                    width={FRAME_CORNER_SIZE}
                    height={FRAME_CORNER_SIZE}
                    borderTopWidth={FRAME_CORNER_THICKNESS}
                    borderRightWidth={FRAME_CORNER_THICKNESS}
                    borderColor="white"
                    borderTopRightRadius={FRAME_BORDER_RADIUS}
                />

                <Box
                    position="absolute"
                    bottom={0}
                    left={0}
                    width={FRAME_CORNER_SIZE}
                    height={FRAME_CORNER_SIZE}
                    borderBottomWidth={FRAME_CORNER_THICKNESS}
                    borderLeftWidth={FRAME_CORNER_THICKNESS}
                    borderColor="white"
                    borderBottomLeftRadius={FRAME_BORDER_RADIUS}
                />

                <Box
                    position="absolute"
                    bottom={0}
                    right={0}
                    width={FRAME_CORNER_SIZE}
                    height={FRAME_CORNER_SIZE}
                    borderBottomWidth={FRAME_CORNER_THICKNESS}
                    borderRightWidth={FRAME_CORNER_THICKNESS}
                    borderColor="white"
                    borderBottomRightRadius={FRAME_BORDER_RADIUS}
                />
            </Box>
        </Box>
    );
};

export default QRFrameOverlay;
