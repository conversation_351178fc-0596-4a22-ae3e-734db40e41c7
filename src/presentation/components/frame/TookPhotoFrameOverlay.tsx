import React from "react";
import Svg, { Defs, Mask, Rect } from "react-native-svg";

import { Box } from "../ui";

import { fullHeight, fullWidth } from "@/shared/helper";

const TookPhotoFrameOverlay = () => {
    // Tính toán kích thước frame dựa trên kích thước màn hình
    // L<PERSON>y kích thước nhỏ hơn giữa chiều rộng và chiều cao, và để lại một chút margin
    const frameSize = Math.min(fullWidth, fullHeight) * 0.8;
    const borderRadius = 24;

    // Căn giữa frame
    const centerX = fullWidth / 2;
    const centerY = fullHeight / 2.2;
    const frameLeft = centerX - frameSize / 2;
    // Điều chỉnh vị trí frame lên trên một chút, nhưng tỷ lệ với kích thước màn hình
    const frameTop = centerY - frameSize / 2 - fullHeight * 0.15;

    return (
        <Box className="absolute top-0 left-0 right-0 bottom-0">
            <Svg height={fullHeight} width={fullWidth}>
                <Defs>
                    <Mask id="mask" x="0" y="0" height="100%" width="100%">
                        <Rect height="100%" width="100%" fill="white" />

                        <Rect
                            x={frameLeft}
                            y={frameTop}
                            width={frameSize}
                            height={frameSize + 200}
                            rx={borderRadius}
                            ry={borderRadius}
                            fill="black"
                        />
                    </Mask>
                </Defs>

                <Rect height="100%" width="100%" fill="rgba(0, 0, 0, 0.6)" mask="url(#mask)" />
            </Svg>
        </Box>
    );
};

export default TookPhotoFrameOverlay;
