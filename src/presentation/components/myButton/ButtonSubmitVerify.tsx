import { useNavigation } from "@react-navigation/native";
import React from "react";

import { MyTouchable } from "../touchable";
import { Box, HStack, Text } from "../ui";

import MyButton from "./MyButton";

import { RouteName } from "@/shared/constants";

type ButtonSubmitVerifyProps = {
    handleVerification?: () => void;
    needHelp?: boolean;
    title?: string;
    isLoading?: boolean;
};

const ButtonSubmitVerify: React.FC<ButtonSubmitVerifyProps> = ({
    handleVerification,
    needHelp = true,
    title = "Submit",
    isLoading = false
}) => {
    const navigation = useNavigation();

    const handleNeedHelp = React.useCallback(() => {
        navigation.navigate(RouteName.Contact);
    }, [navigation]);
    return (
        <Box className="px-4 gap-y-4">
            {needHelp && (
                <HStack className="items-center justify-center gap-x-2">
                    <Text>Need help?</Text>
                    <MyTouchable onPress={handleNeedHelp} disabled={isLoading}>
                        <Text className="text-blue font-semibold">Message Us</Text>
                    </MyTouchable>
                </HStack>
            )}
            <MyButton text={title} onPress={handleVerification} isLoading={isLoading} />
        </Box>
    );
};

export default ButtonSubmitVerify;
