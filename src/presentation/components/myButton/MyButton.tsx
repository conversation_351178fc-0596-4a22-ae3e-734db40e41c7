import React from "react";
import { ActivityIndicator, DimensionValue, StyleProp, ViewStyle } from "react-native";

import { Button, Image, Text } from "../ui";

type MyButtonProps = {
    onPress?: () => void;
    icon?: number;
    text?: string;
    disabled?: boolean;
    isLoading?: boolean;
    variant?: "primary" | "secondary" | "outline";
    style?: StyleProp<ViewStyle>;
    width?: DimensionValue;
    height?: DimensionValue;
    className?: string;
    titleSize?: number;
};

const MyButton: React.FC<MyButtonProps> = ({
    onPress,
    icon,
    text,
    disabled = false,
    isLoading = false,
    variant = "primary",
    style,
    width = "100%",
    height = 63,
    className,
    titleSize = 16
}) => {
    const isButtonDisabledRef = React.useRef(false);
    const throttleTime = 1000;
    const buttonStyle = React.useMemo(() => {
        switch (variant) {
            case "secondary":
                return "bg-buttonSecondaryColor rounded-xl";
            case "outline":
                return "bg-lightBlue rounded-xl";
            default:
                return "bg-darkBlue rounded-xl";
        }
    }, [variant]);

    const textStyle = React.useMemo(() => {
        switch (variant) {
            case "secondary":
                return "text-darkBlue font-bold";
            case "outline":
                return "text-darkBlue font-bold";
            default:
                return "text-white font-bold";
        }
    }, [variant]);

    const renderContent = React.useMemo(() => {
        if (isLoading) {
            return <ActivityIndicator size="small" color="white" />;
        }
        return (
            <>
                {icon && <Image source={icon} className="w-[24px] h-[24px]" alt="icon" />}
                <Text
                    fontSize={titleSize}
                    className={textStyle}
                    numberOfLines={1}
                    adjustsFontSizeToFit
                    minimumFontScale={0.7}>
                    {text}
                </Text>
            </>
        );
    }, [icon, isLoading, text, textStyle, titleSize]);

    const handleOnPress = React.useCallback(() => {
        if (isLoading) return;
        if (isButtonDisabledRef.current) return;

        isButtonDisabledRef.current = true;
        onPress?.();

        setTimeout(() => {
            isButtonDisabledRef.current = false;
        }, throttleTime);
    }, [isLoading, onPress, throttleTime]);

    return (
        <Button
            className={buttonStyle + " " + className}
            height={height}
            onPress={handleOnPress}
            disabled={disabled}
            style={style}
            width={width}
            borderRadius={32}>
            {renderContent}
        </Button>
    );
};

export default MyButton;
