import { useNavigation } from "@react-navigation/native";
import React from "react";
import { ImageSourcePropType } from "react-native";

import { Image } from "../ui";
import Touchable from "../ui/touch";

import { ImageAssets } from "@/shared/constants";

type ButtonBackProps = {
    disabled?: boolean;
    icBack?: ImageSourcePropType;
};

const ButtonBack: React.FC<ButtonBackProps> = ({ disabled = false, icBack }) => {
    const navigation = useNavigation();

    const handleBack = React.useCallback(() => {
        navigation.goBack();
    }, [navigation]);

    return (
        <Touchable onPress={handleBack} disabled={disabled}>
            <Image source={icBack || ImageAssets.icBack} className="w-[24px] h-[24px]" alt="back" />
        </Touchable>
    );
};

export default ButtonBack;
