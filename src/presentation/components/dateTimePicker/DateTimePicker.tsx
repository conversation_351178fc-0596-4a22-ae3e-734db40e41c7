import React from "react";
import DatePicker from "react-native-date-picker";

import { useDeepEffect } from "@/presentation/hooks";

import { usePayload } from "./DateTime.Hook";

const DateTimePicker = () => {
    const [date, setDate] = React.useState<Date>(new Date());
    const [isOpenDate, setIsOpenDate] = React.useState(false);
    const { isOpen, payload } = usePayload();

    useDeepEffect(() => {
        if (isOpen && payload) {
            const newDate = payload.value ? new Date(payload.value) : new Date();
            setDate(newDate);
            setIsOpenDate(true);
        }
    }, [isOpen, payload]);

    const handleConfirm = React.useCallback(
        (value: Date) => {
            if (payload?.onConfirm) {
                payload.onConfirm(value);
            }
            setDate(value);
            setIsOpenDate(false);
        },
        [payload]
    );

    const handleCancel = React.useCallback(() => {
        payload?.onCancel?.();
        setIsOpenDate(false);
    }, [payload]);

    const _renderDateTime = React.useMemo(() => {
        const currentDate = !isNaN(date.getTime()) ? date : new Date();
        switch (payload?.mode) {
            case "date":
                return (
                    <DatePicker
                        mode="date"
                        modal
                        open={isOpenDate}
                        date={currentDate}
                        maximumDate={new Date()}
                        onConfirm={handleConfirm}
                        onCancel={handleCancel}
                    />
                );
            case "time":
                return (
                    <DatePicker
                        mode="time"
                        modal
                        open={isOpenDate}
                        date={currentDate}
                        onConfirm={handleConfirm}
                        onCancel={handleCancel}
                        is24hourSource="locale"
                        locale="en-GB"
                    />
                );
            default:
                return;
        }
    }, [date, handleCancel, handleConfirm, isOpenDate, payload?.mode]);

    return <React.Fragment>{_renderDateTime}</React.Fragment>;
};

export default DateTimePicker;
