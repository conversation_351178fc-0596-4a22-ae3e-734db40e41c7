import React from "react";

import { useDeepEffect } from "@/presentation/hooks";

import { DateTimeObs } from "@/data/services/observable";

export const usePayload = (): {
    isOpen: boolean;
    payload?: DateTimeObsPayload;
} => {
    const [isOpen, setIsOpen] = React.useState<boolean>(false);
    const [payload, setPayload] = React.useState<DateTimeObsPayload>();

    React.useEffect(() => {
        const subscription = DateTimeObs.subscribe((params: DateTimeObsPayload) => {
            setPayload(params);
        });

        return () => subscription.unsubscribe();
    }, []);

    useDeepEffect(() => {
        if (!payload) return;
        setIsOpen(true);
    }, [isOpen, payload]);

    return React.useMemo(
        () => ({
            isOpen,
            payload
        }),
        [isOpen, payload]
    );
};
