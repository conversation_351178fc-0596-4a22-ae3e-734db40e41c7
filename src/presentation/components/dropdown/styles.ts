import { ColorValue, StyleSheet, TextStyle, ViewStyle } from "react-native";

import { getColor } from "@/presentation/hooks";

type TextAlign = "auto" | "left" | "right" | "center" | "justify" | undefined;

export const createStyles = {
    buttonText: (textDropdownAlign: TextAlign, isSelected: boolean): TextStyle => ({
        textAlign: textDropdownAlign,
        color: isSelected ? "black" : getColor("gray2"),
        fontFamily: "Poppins-Regular",
        fontSize: 14
    }),
    button: (
        borderWidth?: number,
        borderColor?: ColorValue,
        backgroundColor?: ColorValue,
        disabled?: boolean
    ): ViewStyle => ({
        width: "100%",
        borderRadius: 16,
        paddingHorizontal: 20,
        paddingVertical: 12,
        borderWidth,
        borderColor,
        backgroundColor: disabled ? getColor("inputDisable") : backgroundColor,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        height: 50
    })
};

export const styles = StyleSheet.create({
    modalOverlay: {
        flex: 1,
        backgroundColor: "rgba(0, 0, 0, 0.2)"
    },
    dropdown: {
        position: "absolute",
        backgroundColor: "white",
        borderRadius: 10,
        elevation: 5,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        zIndex: 1000,
        overflow: "hidden"
    },
    rowText: {
        textAlign: "left",
        fontFamily: "Poppins-Regular",
        fontSize: 15,
        paddingVertical: 10,
        paddingHorizontal: 15
    },
    row: {
        borderBottomWidth: 1,
        borderBottomColor: "#f0f0f0"
    },
    selectedRow: {
        backgroundColor: "#f5f5f5"
    },
    selectedRowText: {
        textAlign: "left",
        fontWeight: "500",
        color: getColor("primary")
    },
    labelText: {
        fontSize: 15
    }
});
