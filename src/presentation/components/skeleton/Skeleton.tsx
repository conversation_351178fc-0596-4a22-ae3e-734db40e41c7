import React from "react";
import { Animated, DimensionValue, ViewStyle } from "react-native";

type SkeletonProps = {
    width?: DimensionValue;
    height?: DimensionValue;
    borderRadius?: number;
    className?: string;
};

const Skeleton: React.FC<SkeletonProps> = ({ width = "100%", height = 20, borderRadius = 4, className = "" }) => {
    const animatedValue = React.useRef(new Animated.Value(0)).current;

    React.useEffect(() => {
        const animation = Animated.loop(
            Animated.sequence([
                Animated.timing(animatedValue, {
                    toValue: 1,
                    duration: 1000,
                    useNativeDriver: true
                }),
                Animated.timing(animatedValue, {
                    toValue: 0,
                    duration: 1000,
                    useNativeDriver: true
                })
            ])
        );

        animation.start();

        return () => animation.stop();
    }, [animatedValue]);

    const opacity = animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [0.3, 0.7]
    });

    const style: ViewStyle = {
        width,
        height,
        borderRadius,
        backgroundColor: "#E1E1E1",
        opacity: opacity as any
    };

    return <Animated.View style={style} className={className} />;
};

export default Skeleton;
