import { useNavigation } from "@react-navigation/native";
import React from "react";

import { BoxCard } from "../boxCard";
import { Box, HStack, IconComponent, Image, Text } from "../ui";

import { ShowBottomSheetObs } from "@/data/services/observable";
import { ImageAssets, RouteName } from "@/shared/constants";

const RewardCard = () => {
    const navigation = useNavigation();

    const handleMyVoucher = React.useCallback(() => {
        navigation.navigate(RouteName.MyVoucher);
    }, [navigation]);

    const handleInfoPress = React.useCallback(() => {
        ShowBottomSheetObs.action({
            title: "CO2 Points",
            message: "1 CO2 point equals to {{value}}",
            titleButtonConfirm: "OKay"
        });
    }, []);

    return (
        <BoxCard>
            <Box className="px-5">
                <Box className="mb-4">
                    <HStack className="items-center justify-between py-4" onPress={handleInfoPress}>
                        <Image
                            source={ImageAssets.co2Points}
                            className="h-[24px]"
                            resizeMode="contain"
                            alt="CO2 Points"
                            tintColor="white"
                        />
                        <HStack className="items-center gap-2">
                            <Image source={ImageAssets.icPoint} className="w-[24px] h-[24px]" alt="Point" />
                            <Text className="text-[24px] font-bold text-white">8,934</Text>
                            <IconComponent name="info" font="feather" size={20} color="white" />
                        </HStack>
                    </HStack>

                    <Box>
                        <Text className="text-white font-bold">400 CO₂ Points left to earn</Text>
                        <Text className="text-white text-[10px]">
                            Daily limit applies. No points earned beyond the limit.
                        </Text>
                    </Box>
                </Box>
                <Image
                    source={ImageAssets.lineWhite}
                    className="h-[2px] w-full"
                    resizeMode="cover"
                    alt="Line"
                    tintColor="white"
                />
                <HStack className="items-center justify-between py-4" onPress={handleMyVoucher}>
                    <HStack className="items-center gap-2">
                        <Image
                            source={ImageAssets.icVoucher}
                            className="h-[14px] w-[20px]"
                            resizeMode="cover"
                            alt="voucher"
                            tintColor="white"
                        />
                        <Text className="text-white font-bold">My Voucher</Text>
                    </HStack>
                    <IconComponent name="chevron-right" font="feather" size={20} color="white" />
                </HStack>
            </Box>
        </BoxCard>
    );
};

export default RewardCard;
