import React from "react";

import { MyButton } from "../myButton";
import { Image, Text, VStack } from "../ui";

import { ImageAssets } from "@/shared/constants";

type EmptyStateProps = {
    title: string;
    description: string;
    buttonText?: string;
    onButtonPress?: () => void;
};

const EmptyState: React.FC<EmptyStateProps> = ({ title, description, buttonText, onButtonPress }) => {
    return (
        <VStack className="items-center justify-center flex-1" space="lg">
            <Image
                source={ImageAssets.emptyState}
                alt="Empty state"
                className="w-full h-[200px]"
                resizeMode="contain"
            />
            <Text className="text-center font-bold text-lg">{title}</Text>
            <Text className="text-center">{description}</Text>
            {buttonText && <MyButton variant="secondary" text={buttonText} onPress={onButtonPress} />}
        </VStack>
    );
};

export default EmptyState;
