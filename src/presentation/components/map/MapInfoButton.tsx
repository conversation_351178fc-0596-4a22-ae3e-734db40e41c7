import { useActionSheet } from "@expo/react-native-action-sheet";
import React from "react";

import { getColor } from "@/presentation/hooks";

import { MyTouchable } from "../touchable";
import { IconComponent } from "../ui";

const MapInfoButton = () => {
    const { showActionSheetWithOptions } = useActionSheet();

    const openActionSheet = () => {
        const options = ["© OpenStreetMap Contributors", "Cancel"];
        const cancelButtonIndex = 1;

        showActionSheetWithOptions(
            {
                options,
                cancelButtonIndex,
                title: "Map Attribution"
            },
            () => {}
        );
    };

    return (
        <MyTouchable
            className="absolute left-5 bottom-[35px] bg-white p-2 rounded-full shadow-md z-10"
            onPress={openActionSheet}>
            <IconComponent name="info" font="feather" size={16} color={getColor("darkGray")} />
        </MyTouchable>
    );
};

export default MapInfoButton;
