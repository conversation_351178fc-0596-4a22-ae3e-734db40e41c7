import { MapViewRef } from "@maplibre/maplibre-react-native";

export type MapMarker = {
    id: string;
    coordinate: [number, number];
    title?: string;
    description?: string;
    iconImage?: any;
    category?: string;
};

export interface MapComponentProps {
    markers?: MapMarker[];
    initialRegion?: {
        longitude: number;
        latitude: number;
        zoomLevel: number;
    };
    onMarkerPress?: (marker: MapMarker) => void;
    userLocation?: {
        longitude: number;
        latitude: number;
    } | null;
}

export type ExtendedMapViewRef = MapViewRef & {
    moveCameraToLocation?: (
        longitude: number,
        latitude: number,
        zoomLevel?: number,
        animationDuration?: number
    ) => void;
    enableUserLocationFollowing?: (enable?: boolean) => void;
};
