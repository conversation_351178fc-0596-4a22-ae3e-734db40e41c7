import {
    Camera,
    CameraRef,
    MapView,
    PointAnnotation,
    requestAndroidLocationPermissions,
    UserLocation
} from "@maplibre/maplibre-react-native";
import { CameraStop } from "@maplibre/maplibre-react-native/lib/typescript/commonjs/src/components/Camera";
import React from "react";

import { MyTouchable } from "../touchable";
import { Box, Image } from "../ui";

import { ExtendedMapViewRef, MapComponentProps, MapMarker } from "./types";

import { ImageAssets } from "@/shared/constants";
import { BATCH_DELAY, fullHeight, fullWidth, isIos, MARKER_BATCH_SIZE, MAX_MARKERS_TO_RENDER } from "@/shared/helper";

export type { ExtendedMapViewRef, MapMarker } from "./types";

const DEFAULT_LONGITUDE = 103.8198;
const DEFAULT_LATITUDE = 1.3521;
const DEFAULT_ZOOM_LEVEL = 16;

const MapComponent = React.forwardRef<ExtendedMapViewRef, MapComponentProps>(
    (
        {
            markers = [],
            initialRegion = { longitude: DEFAULT_LONGITUDE, latitude: DEFAULT_LATITUDE, zoomLevel: DEFAULT_ZOOM_LEVEL },
            onMarkerPress,
            userLocation
        },
        ref
    ) => {
        const [currentUserLocation, setCurrentUserLocation] = React.useState<{
            longitude: number;
            latitude: number;
        } | null>(userLocation || null);

        const [_forceRender, setForceRender] = React.useState(0);
        const [visibleMarkersCount, setVisibleMarkersCount] = React.useState(0);
        const [isMarkersLoading, setIsMarkersLoading] = React.useState(false);
        const cameraRef = React.useRef<CameraRef>(null);
        const markerVisibilityRef = React.useRef<Record<string, boolean>>({});
        const prevMarkersRef = React.useRef(markers);
        const timersRef = React.useRef<number[]>([]);
        const shouldFollowUserRef = React.useRef<boolean>(false);
        const initialLocationSetRef = React.useRef<boolean>(false);
        const markersLoadedRef = React.useRef<boolean>(false);
        const initialMapSetupRef = React.useRef<boolean>(false);
        const isUnmountedRef = React.useRef<boolean>(false);

        React.useEffect(() => {
            return () => {
                isUnmountedRef.current = true;
                timersRef.current.forEach(clearTimeout);
                timersRef.current = [];
            };
        }, []);

        React.useEffect(() => {
            const requestPermission = async () => {
                await requestAndroidLocationPermissions();
            };
            requestPermission();
        }, []);

        React.useEffect(() => {
            if (userLocation && !initialLocationSetRef.current) {
                setCurrentUserLocation(userLocation);
                initialLocationSetRef.current = true;
            }
        }, [userLocation]);

        React.useEffect(() => {
            const refreshTimes = isIos ? [800, 2000] : [200, 800, 2000];
            const timers = refreshTimes.map((time) =>
                setTimeout(() => {
                    if (!isUnmountedRef.current) {
                        setForceRender((prev) => prev + 1);
                    }
                }, time)
            );

            timersRef.current = [...timersRef.current, ...timers];

            return () => {
                timers.forEach(clearTimeout);
            };
        }, []);

        const processMarkers = React.useCallback((markersToProcess: MapMarker[]) => {
            if (!markersToProcess || !Array.isArray(markersToProcess) || markersToProcess.length === 0) {
                return [];
            }

            const batchSize = 50;
            const validMarkers: MapMarker[] = [];

            for (let i = 0; i < markersToProcess.length; i += batchSize) {
                const batch = markersToProcess.slice(i, i + batchSize);

                for (const marker of batch) {
                    if (
                        marker &&
                        marker.id &&
                        marker.coordinate &&
                        Array.isArray(marker.coordinate) &&
                        marker.coordinate.length === 2 &&
                        !isNaN(Number(marker.coordinate[0])) &&
                        !isNaN(Number(marker.coordinate[1]))
                    ) {
                        validMarkers.push(marker);
                    }
                }
            }

            return validMarkers;
        }, []);

        const visibleMarkers = React.useMemo(() => {
            try {
                return processMarkers(markers);
            } catch (err) {
                console.error("Error in visibleMarkers:", err);
                return [];
            }
        }, [markers, processMarkers]);

        React.useEffect(() => {
            if (isUnmountedRef.current) return;

            setIsMarkersLoading(true);
            setVisibleMarkersCount(0);
            markersLoadedRef.current = false;

            const loadMarkers = () => {
                if (isUnmountedRef.current) return;

                const totalMarkers = visibleMarkers.length;
                const maxMarkers = Math.min(totalMarkers, MAX_MARKERS_TO_RENDER);

                // Clear existing timers
                timersRef.current.forEach(clearTimeout);
                timersRef.current = [];

                if (maxMarkers === 0) {
                    setIsMarkersLoading(false);
                    markersLoadedRef.current = true;
                    return;
                }

                for (let i = 0; i < maxMarkers; i += MARKER_BATCH_SIZE) {
                    const timer = setTimeout(
                        () => {
                            if (isUnmountedRef.current) return;

                            setVisibleMarkersCount((prev) => {
                                const newCount = Math.min(prev + MARKER_BATCH_SIZE, maxMarkers);
                                if (newCount >= maxMarkers) {
                                    markersLoadedRef.current = true;
                                    setIsMarkersLoading(false);
                                }
                                return newCount;
                            });
                        },
                        (i / MARKER_BATCH_SIZE) * BATCH_DELAY
                    );

                    timersRef.current.push(timer);
                }
            };

            const initialDelay = isIos ? 500 : 200;
            const initialLoadTimer = setTimeout(loadMarkers, initialDelay);
            timersRef.current.push(initialLoadTimer);

            return () => {
                timersRef.current.forEach(clearTimeout);
                timersRef.current = [];
            };
        }, [visibleMarkers]);

        const mapStyle = React.useMemo(
            () => ({
                version: 8,
                sources: {
                    cartodb: {
                        type: "raster",
                        tiles: [
                            "https://a.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}.png",
                            "https://b.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}.png",
                            "https://c.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}.png",
                            "https://d.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}.png"
                        ],
                        tileSize: 256,
                        attribution: "© OpenStreetMap contributors © CARTO",
                        maxZoom: 20
                    }
                },
                layers: [
                    {
                        id: "cartodb",
                        type: "raster",
                        source: "cartodb",
                        paint: {
                            "raster-fade-duration": 100
                        }
                    }
                ]
            }),
            []
        );

        const handleMarkerPress = React.useCallback(
            (marker: MapMarker) => {
                if (cameraRef.current && marker.coordinate) {
                    cameraRef.current.setCamera({
                        centerCoordinate: marker.coordinate,
                        zoomLevel: 15,
                        animationDuration: 500
                    });
                }

                if (onMarkerPress) {
                    onMarkerPress(marker);
                }
            },
            [onMarkerPress]
        );

        const trackMarkerVisibility = React.useCallback((markerId: string, isVisible: boolean) => {
            markerVisibilityRef.current = {
                ...markerVisibilityRef.current,
                [markerId]: isVisible
            };
        }, []);

        const handleUserLocationUpdate = React.useCallback(
            (location: { coords: { longitude: number; latitude: number } }) => {
                if (isUnmountedRef.current) return;

                const { longitude, latitude } = location.coords;
                setCurrentUserLocation({ longitude, latitude });

                if (!initialMapSetupRef.current) {
                    initialMapSetupRef.current = true;

                    const timer = setTimeout(
                        () => {
                            if (isUnmountedRef.current) return;

                            if (cameraRef.current) {
                                cameraRef.current.setCamera({
                                    centerCoordinate: [longitude, latitude],
                                    zoomLevel: initialRegion.zoomLevel,
                                    animationDuration: 0
                                });
                            }
                        },
                        isIos ? 200 : 100
                    );

                    timersRef.current.push(timer);
                }

                if (!markersLoadedRef.current && !isMarkersLoading) {
                    const timer = setTimeout(
                        () => {
                            if (!isUnmountedRef.current) {
                                setForceRender((prev) => prev + 1);
                            }
                        },
                        isIos ? 500 : 300
                    );

                    timersRef.current.push(timer);
                }
            },
            [initialRegion.zoomLevel, isMarkersLoading]
        );

        React.useEffect(() => {
            if (isUnmountedRef.current) return;

            const prevMarkers = prevMarkersRef.current;

            const hasSignificantChange =
                prevMarkers.length !== markers.length ||
                (markers.length > 0 && prevMarkers.length > 0 && markers[0].category !== prevMarkers[0].category);

            prevMarkersRef.current = [...markers];

            if (hasSignificantChange) {
                // Clear existing timers first
                timersRef.current.forEach(clearTimeout);
                timersRef.current = [];

                setIsMarkersLoading(true);
                setVisibleMarkersCount(0);
                markersLoadedRef.current = false;

                const refreshTimes = isIos ? [600, 1200] : [200, 500, 800];
                const timers = refreshTimes.map((time) =>
                    setTimeout(() => {
                        if (!isUnmountedRef.current) {
                            setForceRender((prev) => prev + 1);
                        }
                    }, time)
                );

                timersRef.current = [...timersRef.current, ...timers];

                return () => {
                    timers.forEach(clearTimeout);
                };
            }
        }, [markers]);

        React.useImperativeHandle(ref, () => {
            const moveCameraToLocation = (
                longitude: number,
                latitude: number,
                zoomLevel: number = 15,
                animationDuration: number = 800
            ) => {
                if (cameraRef.current) {
                    cameraRef.current.setCamera({
                        centerCoordinate: [longitude, latitude],
                        zoomLevel,
                        animationDuration,
                        animationMode: "easeTo"
                    });
                }
            };

            const enableUserLocationFollowing = (enable: boolean = true) => {
                shouldFollowUserRef.current = enable;

                if (enable && currentUserLocation && cameraRef.current) {
                    cameraRef.current.setCamera({
                        centerCoordinate: [currentUserLocation.longitude, currentUserLocation.latitude],
                        zoomLevel: initialRegion.zoomLevel,
                        animationDuration: 500,
                        animationMode: "easeTo"
                    });
                }
            };

            return {
                getPointInView: async () => [0, 0] as [number, number],
                getCoordinateFromView: async () => [0, 0] as [number, number],
                getVisibleBounds: async () =>
                    [
                        [0, 0],
                        [0, 0]
                    ] as [[number, number], [number, number]],
                queryRenderedFeaturesAtPoint: async () => ({ type: "FeatureCollection", features: [] }),
                queryRenderedFeaturesInRect: async () => ({ type: "FeatureCollection", features: [] }),
                takeSnap: async () => "",
                getZoom: async () => 0,
                getCenter: async () => [0, 0] as [number, number],
                setSourceVisibility: (_visible: boolean, _sourceId: string, _sourceLayerId?: string | null) => {},
                showAttribution: async () => {},
                setNativeProps: () => {},
                moveCameraToLocation,
                enableUserLocationFollowing
            };
        });

        const renderMarkers = React.useMemo(() => {
            try {
                if (isUnmountedRef.current || isMarkersLoading) {
                    return null;
                }

                const markersToRender = visibleMarkers.slice(0, visibleMarkersCount);

                if (!markersToRender || markersToRender.length === 0) {
                    return null;
                }

                return markersToRender
                    .map((marker, index) => {
                        try {
                            const markerIcon = marker.iconImage || ImageAssets.icMapBatteries;
                            // Use stable key without _forceRender to prevent unnecessary re-renders
                            const stableKey = `marker-${marker.id}-${marker.category || "default"}-${index}`;

                            return (
                                <PointAnnotation
                                    key={stableKey}
                                    id={marker.id}
                                    coordinate={marker.coordinate}
                                    onSelected={() => {
                                        try {
                                            trackMarkerVisibility(marker.id, true);
                                        } catch (err) {
                                            /* empty */
                                        }
                                    }}
                                    onDeselected={() => {
                                        try {
                                            trackMarkerVisibility(marker.id, false);
                                        } catch (err) {
                                            /* empty */
                                        }
                                    }}>
                                    <MyTouchable
                                        className="z-10 w-[75px] h-[75px]"
                                        onPress={() => handleMarkerPress(marker)}>
                                        <Image
                                            source={ImageAssets.binsAndCollection}
                                            className="w-full h-full"
                                            resizeMode="contain"
                                            alt="bins and collection"
                                        />
                                        <Box className="absolute bottom-1 right-0 top-0 left-0 items-center justify-center">
                                            <Box className="w-[20px] h-[20px] rounded-full">
                                                <Image
                                                    source={markerIcon}
                                                    className="w-full h-full"
                                                    resizeMode="contain"
                                                    alt="marker icon"
                                                />
                                            </Box>
                                        </Box>
                                    </MyTouchable>
                                </PointAnnotation>
                            );
                        } catch (err) {
                            return null;
                        }
                    })
                    .filter(Boolean);
            } catch (err) {
                return null;
            }
        }, [visibleMarkers, visibleMarkersCount, isMarkersLoading, handleMarkerPress, trackMarkerVisibility]);

        const onDidFinishRenderingMapFully = React.useCallback(() => {
            if (isUnmountedRef.current) return;

            const timer = setTimeout(
                () => {
                    if (!isUnmountedRef.current) {
                        setForceRender((prev) => prev + 1);
                    }
                },
                isIos ? 1000 : 600
            );

            timersRef.current.push(timer);
        }, []);

        const onRegionDidChange = React.useCallback(() => {
            shouldFollowUserRef.current = false;
        }, []);

        const initialCameraSettings: CameraStop = React.useMemo(() => {
            if (userLocation) {
                return {
                    centerCoordinate: [userLocation.longitude, userLocation.latitude],
                    zoomLevel: initialRegion.zoomLevel,
                    animationDuration: 500,
                    animationMode: "easeTo"
                };
            }

            return {
                centerCoordinate: [initialRegion.longitude, initialRegion.latitude],
                zoomLevel: initialRegion.zoomLevel,
                animationDuration: 500,
                animationMode: "easeTo"
            };
        }, [userLocation, initialRegion]);

        return (
            <MapView
                ref={ref}
                style={{ width: fullWidth, height: fullHeight }}
                attributionEnabled={false}
                mapStyle={mapStyle}
                logoEnabled={false}
                compassEnabled={true}
                zoomEnabled={true}
                onDidFinishRenderingMapFully={onDidFinishRenderingMapFully}
                onRegionWillChange={onRegionDidChange}>
                <Camera ref={cameraRef} defaultSettings={initialCameraSettings} />
                <UserLocation visible onUpdate={handleUserLocationUpdate} minDisplacement={10} />

                {renderMarkers}
            </MapView>
        );
    }
);

const MemoizedMapComponent = React.memo(MapComponent, (prevProps, nextProps) => {
    if (prevProps.userLocation?.latitude !== nextProps.userLocation?.latitude) return false;
    if (prevProps.userLocation?.longitude !== nextProps.userLocation?.longitude) return false;

    if (prevProps.markers?.length !== nextProps.markers?.length) return false;

    return false;
});

export default MemoizedMapComponent;
