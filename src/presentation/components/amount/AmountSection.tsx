import React from "react";

import { Box, HStack, Image, Text, VStack } from "../ui";

import { ImageAssets } from "@/shared/constants";

type AmountSectionProps = {
    points: string;
    balance: string;
};

const AmountSection: React.FC<AmountSectionProps> = ({ points, balance }) => (
    <Box className="mx-5 mt-5 bg-gray rounded-xl p-5 gap-y-2">
        <Text className="text-neutralGray text-base">Amount</Text>
        <HStack className="justify-between">
            <HStack className="gap-x-2">
                <Image source={ImageAssets.icPoint} alt="CO2 Points" className="w-[24px] h-[24px]" />
                <VStack>
                    <Text className="text-darkBlue font-bold">CO2 Points</Text>
                    <Text className="text-darkGray">Balance: {balance}</Text>
                </VStack>
            </HStack>
            <Text className="text-darkBlue font-bold">{points}</Text>
        </HStack>
    </Box>
);
export default AmountSection;
