import React from "react";
import { ImageSourcePropType } from "react-native";

import { getColor } from "@/presentation/hooks";

import { MyTouchable } from "../touchable";
import { Box, IconComponent, Image, Text } from "../ui";

import { IconName } from "@/shared/types/icon";

interface ChipProps {
    icon?: string | number | IconName | ImageSourcePropType;
    label: string;
    onPress?: () => void;
    isSelected?: boolean;
    disabled?: boolean;
}

const Chip = ({ icon, label, onPress, isSelected = false, disabled = false }: ChipProps) => {
    const getBgColor = () => {
        if (disabled) return "bg-gray-200";
        if (isSelected) return "bg-blue";
        return "bg-grayCard";
    };

    const getTextColor = () => {
        if (disabled) return "text-gray-400";
        if (isSelected) return "text-white";
        return "text-darkGray";
    };

    const getIconColor = () => {
        if (disabled) return getColor("gray-400");
        if (isSelected) return getColor("white");
        return getColor("blue");
    };

    return (
        <MyTouchable
            className={`flex-row items-center rounded-xl px-2 h-[36px] ${getBgColor()}`}
            onPress={onPress}
            disabled={disabled}>
            {icon && (
                <Box className="mr-2">
                    {typeof icon === "string" ? (
                        <IconComponent name={icon as IconName} font="feather" size={20} color={getIconColor()} />
                    ) : (
                        <Image
                            source={icon}
                            className="w-[20px] h-[20px]"
                            alt={label}
                            tintColor={isSelected ? "white" : disabled ? "gray" : undefined}
                        />
                    )}
                </Box>
            )}
            <Text className={getTextColor()}>{label}</Text>
        </MyTouchable>
    );
};

export default Chip;
