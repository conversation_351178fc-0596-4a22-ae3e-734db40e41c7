import React from "react";
import { StyleSheet } from "react-native";
import { Camera, CameraRuntimeError, Code, useCameraDevice, useCodeScanner } from "react-native-vision-camera";

import { isIos } from "@/shared/helper";

type QRCameraProps = {
    onCodeScanned: (codes: Code[]) => void;
    isTorchOn: boolean;
    onError: (error: CameraRuntimeError) => void;
    isActive: boolean;
};

const QRCamera = React.forwardRef<Camera, QRCameraProps>(({ onCodeScanned, isTorchOn, onError, isActive }, ref) => {
    const device = useCameraDevice("back");

    const regionOfInterest = React.useMemo(() => {
        return {
            x: 0.2,
            y: 0.2,
            width: 0.6,
            height: 0.6
        };
    }, []);

    const codeScanner = useCodeScanner({
        codeTypes: ["qr"],
        onCodeScanned,
        regionOfInterest: isIos ? regionOfInterest : undefined
    });

    return (
        <Camera
            ref={ref}
            style={StyleSheet.absoluteFill}
            device={device!}
            isActive={isActive}
            codeScanner={isActive ? codeScanner : undefined}
            torch={isTorchOn ? "on" : "off"}
            enableZoomGesture={false}
            onError={onError}
            photo={false}
            video={false}
            audio={false}
        />
    );
});

export default QRCamera;
