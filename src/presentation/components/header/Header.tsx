import React from "react";
import { ColorValue, ImageSourcePropType } from "react-native";

import { ButtonBack } from "../myButton";
import { MyTouchable } from "../touchable";
import { Box, HStack, Image, Text } from "../ui";

type HeaderProps = {
    title: React.ReactNode | string;
    rightComponent?: React.ReactNode | string | ImageSourcePropType;
    isShowBack?: boolean;
    tintColor?: ColorValue;
    onPress?: () => void;
    titleColor?: string;
    icBack?: ImageSourcePropType;
};

const Header: React.FC<HeaderProps> = ({
    title,
    rightComponent,
    isShowBack = true,
    tintColor,
    onPress,
    titleColor,
    icBack
}) => {
    const renderRightComponent = React.useMemo(() => {
        if (
            typeof rightComponent === "number" ||
            (rightComponent && typeof rightComponent === "object" && "uri" in rightComponent)
        ) {
            return (
                <MyTouchable onPress={onPress}>
                    <Image
                        source={rightComponent}
                        className="w-6 h-6"
                        alt={typeof rightComponent === "number" ? rightComponent.toString() : "image"}
                        tintColor={tintColor}
                    />
                </MyTouchable>
            );
        }
        return rightComponent as React.ReactNode;
    }, [rightComponent, tintColor, onPress]);

    const renderTitle = React.useMemo(() => {
        if (typeof title === "string") {
            return <Text className={`text-[20px] font-bold ${titleColor ? `text-${titleColor}` : ""}`}>{title}</Text>;
        }
        return title;
    }, [title, titleColor]);

    return (
        <Box className="px-4 pb-4">
            <HStack className="items-center justify-between">
                <HStack className="gap-3 items-center">
                    {isShowBack && <ButtonBack icBack={icBack} />}
                    {renderTitle}
                </HStack>
                {renderRightComponent}
            </HStack>
        </Box>
    );
};

export default Header;
