import { LinearGradient } from "expo-linear-gradient";
import React from "react";

import { getColor } from "@/presentation/hooks";

import { MyButton } from "../myButton";
import { Box, HStack, Text } from "../ui";

import { ImageAssets } from "@/shared/constants";

type OptionLoginProps = {
    title: string;
    onPress: () => void;
};

const OptionLogin = ({ title, onPress }: OptionLoginProps) => {
    return (
        <>
            <HStack className="items-center gap-2">
                <LinearGradient
                    colors={["white", getColor("blue")]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={{ height: 2, flex: 1, borderRadius: 1 }}
                />

                <Text className="text-darkGray font-bold text-center mx-2 whitespace-nowrap">
                    Login as School/Entreprises
                </Text>
                <LinearGradient
                    colors={[getColor("blue"), "white"]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={{ height: 2, flex: 1, borderRadius: 1 }}
                />
            </HStack>
            <Box className="items-center">
                <Box className="w-2/3">
                    <MyButton
                        icon={ImageAssets.icOrganization}
                        text={title}
                        className="mt-3 mb-3"
                        onPress={onPress}
                        variant="outline"
                    />
                </Box>
            </Box>
        </>
    );
};

export default OptionLogin;
