import React from "react";
import { StyleProp, ViewStyle } from "react-native";
import RBSheet, { RBSheetProps } from "react-native-raw-bottom-sheet";

import { getColor } from "@/presentation/hooks";

const BottomSheetStyles: {
    wrapper?: StyleProp<ViewStyle>;
    container?: StyleProp<ViewStyle>;
    draggableIcon?: StyleProp<ViewStyle>;
} = {
    container: {
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        paddingBottom: 15
    }
};

type BottomSheetProps = RBSheetProps & {
    draggableIconColor?: string;
};

const BottomSheetCustom = React.forwardRef<RBSheet, BottomSheetProps>(
    (
        {
            draggableIconColor = getColor("lightGray2"),
            children,
            height = 350,
            closeOnDragDown = true,
            closeOnPressMask = true,
            dragFromTopOnly = true,
            onClose,
            ...props
        },
        ref
    ) => {
        const bottomSheetStyle: {
            wrapper?: StyleProp<ViewStyle>;
            container?: StyleProp<ViewStyle>;
            draggableIcon?: StyleProp<ViewStyle>;
        } = React.useMemo(() => {
            return {
                ...BottomSheetStyles,
                draggableIcon: {
                    backgroundColor: draggableIconColor
                }
            };
        }, [draggableIconColor]);

        return (
            <RBSheet
                {...props}
                ref={ref}
                closeOnDragDown={closeOnDragDown}
                onClose={onClose}
                closeOnPressMask={closeOnPressMask}
                dragFromTopOnly={dragFromTopOnly}
                customStyles={bottomSheetStyle}
                height={height}>
                {children}
            </RBSheet>
        );
    }
);

export default React.memo(BottomSheetCustom);
