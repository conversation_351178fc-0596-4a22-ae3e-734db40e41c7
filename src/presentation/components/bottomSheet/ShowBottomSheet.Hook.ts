import React from "react";

import { useDeepEffect } from "@/presentation/hooks";

import { ShowBottomSheetObs } from "@/data/services/observable";

export const usePayload = (): {
    isOpen: boolean;
    payload?: ShowBottomSheetObsPayload;
} => {
    const [isOpen, setIsOpen] = React.useState<boolean>(false);
    const [payload, setPayload] = React.useState<ShowBottomSheetObsPayload>();

    React.useEffect(() => {
        const subscription = ShowBottomSheetObs.subscribe((params: ShowBottomSheetObsPayload) => {
            setPayload(params);
        });

        return () => subscription.unsubscribe();
    }, []);

    useDeepEffect(() => {
        if (!payload) return;
        setIsOpen(true);
    }, [isOpen, payload]);

    return React.useMemo(
        () => ({
            isOpen,
            payload
        }),
        [isOpen, payload]
    );
};
