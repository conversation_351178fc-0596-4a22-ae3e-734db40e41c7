import React, { useEffect, useRef } from "react";
import { Dimensions, View } from "react-native";

import HighlightWalkthrough from "./HighlightWalkthrough";

import { isIos, markAppAsLaunched } from "@/shared/helper";

const { width: screenWidth } = Dimensions.get("window");

export type PointsWalkthroughProps = {
    isVisible: boolean;
    onClose?: () => void;
    onNext?: () => void;
    currentStep?: number;
    totalSteps?: number;
    children?: React.ReactNode;
};

const PointsWalkthrough: React.FC<PointsWalkthroughProps> = ({
    isVisible,
    onClose,
    onNext,
    currentStep = 5,
    totalSteps = 5,
    children
}) => {
    const childRef = useRef<View>(null);
    const [elementPosition, setElementPosition] = React.useState({ x: 0, y: 0, width: 0, height: 0 });
    useEffect(() => {
        if (childRef.current && isVisible) {
            childRef.current.measure((x, y, width, height, pageX, pageY) => {
                setElementPosition({
                    x: pageX,
                    y: pageY - (isIos ? 25 : 20),
                    width,
                    height
                });
            });
        }
    }, [isVisible]);

    const highlightPosition = React.useMemo(
        () => ({
            x: elementPosition.x,
            y: elementPosition.y,
            size: Math.max(elementPosition.width, elementPosition.height)
        }),
        [elementPosition]
    );

    const highlightShape = React.useMemo(
        () => ({
            width: elementPosition.width + 20,
            height: elementPosition.height + 10,
            cornerRadius: elementPosition.height / 2
        }),
        [elementPosition]
    );

    const wrappedChildren = React.useMemo(() => {
        return (
            <View ref={childRef} collapsable={false} style={{ opacity: 1 }}>
                {children}
            </View>
        );
    }, [children]);

    const triangleTopRight = React.useMemo(() => {
        if (elementPosition.width === 0) return 0;

        const centerX = screenWidth / 2;
        const elementCenterX = elementPosition.x + elementPosition.width / 2;
        return elementCenterX - centerX;
    }, [elementPosition]);

    const handleOnClose = React.useCallback(async () => {
        await markAppAsLaunched();
        onClose?.();
    }, [onClose]);

    return (
        <HighlightWalkthrough
            isVisible={isVisible}
            onClose={handleOnClose}
            onNext={onNext}
            title="Finish! Instantly earn your CO2 Points"
            content="Redeem points with our partners"
            highlightPosition={highlightPosition}
            highlightShape={highlightShape}
            currentStep={currentStep}
            totalSteps={totalSteps}
            bottomOffset={-250}
            triangleTopRight={triangleTopRight}
            onBack={() => {}}>
            {wrappedChildren}
        </HighlightWalkthrough>
    );
};

export default PointsWalkthrough;
