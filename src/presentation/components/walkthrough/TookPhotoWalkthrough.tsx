import { useNavigation } from "@react-navigation/native";
import React from "react";

import HighlightWalkthrough from "./HighlightWalkthrough";

import { RouteName } from "@/shared/constants";

export type TookPhotoWalkthroughProps = {
    isVisible: boolean;
    onClose: () => void;
    currentStep?: number;
    totalSteps?: number;
    children: React.ReactNode;
};

const TookPhotoWalkthrough: React.FC<TookPhotoWalkthroughProps> = ({
    isVisible,
    onClose,
    currentStep = 1,
    totalSteps = 1,
    children
}) => {
    const navigation = useNavigation();
    const handleNext = React.useCallback(() => {
        navigation.navigate(RouteName.Bottom, {
            screen: RouteName.Home,
            params: { fromWalkthrough: true }
        });
        onClose();
    }, [navigation, onClose]);

    return (
        <HighlightWalkthrough
            isVisible={isVisible}
            onClose={onClose}
            onNext={handleNext}
            title="Took Super Clear Photo"
            content="Take a clear snapshot of the recyclables before disposing them into the bins"
            currentStep={currentStep}
            totalSteps={totalSteps}
            trianglePosition="bottom"
            bottomOffset={100}
            onBack={() => {}}>
            {children}
        </HighlightWalkthrough>
    );
};

export default TookPhotoWalkthrough;
