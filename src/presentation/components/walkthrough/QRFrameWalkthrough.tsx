import { useNavigation } from "@react-navigation/native";
import React from "react";

import HighlightWalkthrough from "./HighlightWalkthrough";

import { RouteName } from "@/shared/constants";

export type QRFrameWalkthroughProps = {
    isVisible: boolean;
    onClose: () => void;
    currentStep?: number;
    totalSteps?: number;
    children: React.ReactNode;
};

const QRFrameWalkthrough: React.FC<QRFrameWalkthroughProps> = ({
    isVisible,
    onClose,
    currentStep = 1,
    totalSteps = 1,
    children
}) => {
    const navigation = useNavigation();
    const handleNext = React.useCallback(() => {
        navigation.navigate(RouteName.ScanStack, { screen: RouteName.TookPhoto, params: { fromWalkthrough: true } });
        onClose();
    }, [navigation, onClose]);

    return (
        <HighlightWalkthrough
            isVisible={isVisible}
            onClose={onClose}
            onNext={handleNext}
            title="Scan the QR Code"
            content="Look out for the QR Code pasted on the bins and chutes"
            currentStep={currentStep}
            totalSteps={totalSteps}
            onBack={() => {}}>
            {children}
        </HighlightWalkthrough>
    );
};

export default QRFrameWalkthrough;
