import React from "react";
import { ImageSourcePropType } from "react-native";

import { MyTouchable } from "../touchable";
import { Box, Image, Text } from "../ui";

type RecyclingItemProps = {
    icon: string | ImageSourcePropType;
    title: string;
    onPress?: () => void;
    isPressed?: boolean;
};

const RecyclingItem: React.FC<RecyclingItemProps> = ({ icon, title, onPress, isPressed }) => {
    return (
        <MyTouchable
            className={`p-4 ${isPressed ? "bg-green" : "bg-gray"} rounded-xl flex-row items-center gap-x-3`}
            onPress={onPress}>
            <Image source={icon} alt="Icon" resizeMode="cover" className="w-[32px] h-[32px]" />
            <Box className="flex-1">
                <Text className="text-base font-medium">{title}</Text>
            </Box>
        </MyTouchable>
    );
};

export default RecyclingItem;
