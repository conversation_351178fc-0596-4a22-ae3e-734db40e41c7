import React from "react";
import { ImageSourcePropType } from "react-native";

import { getColor } from "@/presentation/hooks";

import { MyTouchable } from "../touchable";
import { HStack, IconComponent, Image, Text } from "../ui";

type MenuItemProps = {
    icon: string | ImageSourcePropType;
    title: string;
    onPress?: () => void;
};

const MenuItem: React.FC<MenuItemProps> = ({ icon, title, onPress }) => {
    return (
        <MyTouchable onPress={onPress}>
            <HStack className="items-center justify-between">
                <HStack className="items-center gap-x-3">
                    <Image source={icon} className="w-[24px] h-[24px]" alt="Icon" />
                    <Text className="text-base">{title}</Text>
                </HStack>
                <IconComponent name="chevron-right" font="feather" size={20} color={getColor("darkGray")} />
            </HStack>
        </MyTouchable>
    );
};

export default MenuItem;
