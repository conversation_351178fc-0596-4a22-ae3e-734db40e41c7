import React from "react";

import { MyTouchable } from "../touchable";
import { Box, HStack, Text } from "../ui";

interface VoucherCardProps {
    bgColor: string;
    title: string;
    value: string;
    points: string;
    isBlack?: boolean;
    isGreen?: boolean;
    variant?: "voucher" | "point";
    onPress?: () => void;
}

const VoucherCard: React.FC<VoucherCardProps> = ({
    bgColor,
    title,
    value,
    points,
    isBlack,
    isGreen,
    variant = "point",
    onPress
}) => {
    const renderContent = React.useMemo(() => {
        switch (variant) {
            case "voucher":
                return (
                    <Box className="bg-darkBlue mt-2 py-3 px-4 rounded-full mx-1 mb-2">
                        <HStack className="items-center justify-center gap-x-2">
                            <Text className="text-white text-lg font-medium">{points}</Text>
                        </HStack>
                    </Box>
                );
            case "point":
                return (
                    <>
                        {Number(points) > 0 && (
                            <Box className="px-2">
                                <Box className="bg-darkBlue mt-2 py-3 px-4 rounded-full mx-1 mb-2">
                                    <HStack className="items-center justify-center gap-x-2">
                                        <Box className="w-6 h-6 rounded-full bg-yellow-400" />
                                        <Text className="text-white text-lg font-medium">{points}</Text>
                                    </HStack>
                                </Box>
                            </Box>
                        )}
                    </>
                );
        }
    }, [variant, points]);

    return (
        <MyTouchable className="w-full p-2 " onPress={onPress}>
            <Box className=" bg-grayCard rounded-xl overflow-hidden">
                <Box className="rounded-xl overflow-hidden m-3 ">
                    <Box className="h-[150px] items-center justify-center" backgroundColor={bgColor}>
                        <Text
                            className={`text-2xl font-bold ${isBlack ? "text-black" : isGreen ? "text-green-400" : "text-white"}`}>
                            {title}
                        </Text>
                    </Box>
                </Box>

                {value && (
                    <Box className="bg-lightBlue py-1 px-4 rounded-full self-end mx-3 -mt-[25px]">
                        <Text className="text-sm font-medium">{value}</Text>
                    </Box>
                )}

                {renderContent}
            </Box>
        </MyTouchable>
    );
};

export default VoucherCard;
