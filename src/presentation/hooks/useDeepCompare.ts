import React from "react";

import { compareValue } from "@/shared/helper";

const useDeepMemoize = (value: React.DependencyList) => {
    const ref = React.useRef<React.DependencyList>([]);

    if (!compareValue(value, ref.current)) {
        ref.current = value;
    }

    return ref.current;
};

/**
 * `useDeepCompareEffect` Accepts a function that contains imperative, possibly
 * effectful code.
 *
 * @param effect Imperative function that can return a cleanup function
 * @param dependencies
 * change.
 *
 * Usage note: only use this if `deps` are objects or arrays that contain
 * objects. Otherwise you should just use React.useEffect.
 *
 */
const useDeepEffect = (effect: React.EffectCallback, dependencies: React.DependencyList) => {
    // eslint-disable-next-line react-hooks/exhaustive-deps
    React.useEffect(effect, useDeepMemoize(dependencies));
};

export default useDeepEffect;
