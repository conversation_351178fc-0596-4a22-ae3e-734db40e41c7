import React from "react";

import { postalCodeValidatingSubject } from "@/shared/validations";

export const usePostalCodeValidation = () => {
    const [isValidating, setIsValidating] = React.useState(false);
    const lastValidatedRef = React.useRef<string>("");
    const timeoutRef = React.useRef<ReturnType<typeof setTimeout> | null>(null);

    React.useEffect(() => {
        const subscription = postalCodeValidatingSubject.subscribe((validating) => {
            setIsValidating(validating);
        });

        return () => {
            subscription.unsubscribe();
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    const handlePostalCodeChange = React.useCallback(
        (
            field: string,
            value: string,
            onChangeValue: (field: string, value: string, shouldValidate?: boolean) => void,
            setFieldTouched: (field: string, touched: boolean) => void,
            validateForm: () => Promise<any>
        ) => {
            onChangeValue(field, value, false);

            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }

            if (value.length === 6 && /^\d{6}$/.test(value) && value !== lastValidatedRef.current) {
                lastValidatedRef.current = value;

                timeoutRef.current = setTimeout(() => {
                    setFieldTouched(field, true);
                    validateForm();
                }, 300);
            }
        },
        []
    );

    return {
        isValidating,
        handlePostalCodeChange
    };
};
