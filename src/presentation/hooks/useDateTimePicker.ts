import React from "react";

import { DateTimeObs } from "@/data/services/observable";

type UseDateTimePickerOptions = {
    mode: "date" | "time" | "datetime";
    valueDate?: Date;
    onSuccess?: (value: Date) => void;
    onCancel?: () => void;
};

const useDateTimePicker = () => {
    const openDateTimePicker = React.useCallback(
        ({ mode, valueDate, onSuccess, onCancel }: UseDateTimePickerOptions) => {
            DateTimeObs.action({
                mode,
                value: valueDate,
                onConfirm: (value: Date) => {
                    onSuccess?.(value);
                },
                onCancel
            });
        },
        []
    );

    return { openDateTimePicker };
};

export default useDateTimePicker;
