import * as IntentLauncher from "expo-intent-launcher";
import * as Location from "expo-location";
import React from "react";
import { Alert } from "react-native";

import { ExtendedMapViewRef } from "../components/map";

import { isAndroid } from "@/shared/helper";

const LOCATION_CACHE_DURATION = 30000;

export const useLocationServices = () => {
    const [isLoadingLocation, setIsLoadingLocation] = React.useState(false);
    const [userLocation, setUserLocation] = React.useState<{ longitude: number; latitude: number } | null>(null);
    const lastLocationTimeRef = React.useRef<number>(0);
    const locationCacheRef = React.useRef<Location.LocationObject | null>(null);

    React.useEffect(() => {
        const initializeLocation = async () => {
            try {
                const { status } = await Location.requestForegroundPermissionsAsync();
                if (status === "granted") {
                    const location = await Location.getCurrentPositionAsync({
                        accuracy: Location.Accuracy.Balanced
                    });

                    locationCacheRef.current = location;
                    lastLocationTimeRef.current = Date.now();
                    setUserLocation({
                        longitude: location.coords.longitude,
                        latitude: location.coords.latitude
                    });
                }
            } catch (error) {
                /* empty */
            }
        };

        initializeLocation();
    }, []);

    const goToUserLocation = async (mapRef: React.RefObject<ExtendedMapViewRef | null>) => {
        try {
            setIsLoadingLocation(true);

            if (isAndroid) {
                const hasServicesEnabled = await Location.hasServicesEnabledAsync();
                if (!hasServicesEnabled) {
                    return new Promise((resolve) => {
                        Alert.alert(
                            "Location Services Disabled",
                            "Please enable location services to use this feature",
                            [
                                {
                                    text: "Cancel",
                                    style: "cancel",
                                    onPress: () => {
                                        setIsLoadingLocation(false);
                                        resolve(false);
                                    }
                                },
                                {
                                    text: "Open Settings",
                                    onPress: () => {
                                        IntentLauncher.startActivityAsync(
                                            IntentLauncher.ActivityAction.LOCATION_SOURCE_SETTINGS
                                        );
                                        setIsLoadingLocation(false);
                                        resolve(false);
                                    }
                                }
                            ]
                        );
                    });
                }
            }

            const { status } = await Location.requestForegroundPermissionsAsync();
            if (status !== "granted") {
                setIsLoadingLocation(false);
                Alert.alert("Permission Denied", "Please grant location permissions to use this feature.", [
                    { text: "OK" }
                ]);
                return;
            }

            const now = Date.now();

            if (locationCacheRef.current && now - lastLocationTimeRef.current < LOCATION_CACHE_DURATION) {
                if (mapRef.current && mapRef.current.moveCameraToLocation) {
                    mapRef.current.moveCameraToLocation(
                        locationCacheRef.current.coords.longitude,
                        locationCacheRef.current.coords.latitude,
                        16,
                        500
                    );
                }

                setIsLoadingLocation(false);

                Location.getCurrentPositionAsync({
                    accuracy: Location.Accuracy.Balanced,
                    mayShowUserSettingsDialog: false
                })
                    .then((location) => {
                        locationCacheRef.current = location;
                        lastLocationTimeRef.current = now;
                        setUserLocation({
                            longitude: location.coords.longitude,
                            latitude: location.coords.latitude
                        });

                        if (mapRef.current && mapRef.current.moveCameraToLocation) {
                            mapRef.current.moveCameraToLocation(
                                location.coords.longitude,
                                location.coords.latitude,
                                16,
                                300
                            );
                        }
                    })
                    .catch(() => {
                        /* empty */
                    });

                return;
            }

            const locationPromise = Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.Balanced,
                mayShowUserSettingsDialog: true
            });

            const timeoutPromise = new Promise<Location.LocationObject>((_, reject) => {
                setTimeout(() => {
                    reject(new Error("Location request timed out"));
                }, 5000);
            });

            const location = await Promise.race([locationPromise, timeoutPromise]).catch((error) => {
                if (locationCacheRef.current) {
                    return locationCacheRef.current;
                }
                throw error;
            });

            locationCacheRef.current = location;
            lastLocationTimeRef.current = now;
            setUserLocation({
                longitude: location.coords.longitude,
                latitude: location.coords.latitude
            });

            if (mapRef.current && mapRef.current.moveCameraToLocation) {
                mapRef.current.moveCameraToLocation(location.coords.longitude, location.coords.latitude, 16, 500);
            }

            setIsLoadingLocation(false);
        } catch (error) {
            setIsLoadingLocation(false);
            Alert.alert("Location Error", "Unable to get your current location. Please try again later.", [
                { text: "OK" }
            ]);
        }
    };

    return { goToUserLocation, isLoadingLocation, userLocation };
};
