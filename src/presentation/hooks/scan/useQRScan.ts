import { useFocusEffect, useNavigation } from "@react-navigation/native";
import React from "react";
import { Vibration } from "react-native";
import RBSheet from "react-native-raw-bottom-sheet";
import { Camera, Code, useCameraDevice } from "react-native-vision-camera";

import { useBinDetail } from "../bin";
import { useCameraPermission } from "../camera";
import useRouteParams from "../useRouteParams";

import { getFramePosition } from "@/presentation/components/frame";
import { BinTypes, ImageAssets, RouteName } from "@/shared/constants";
import { BinTypeKey } from "@/shared/constants/BinTypes";
import { fullHeight, fullWidth, isIos } from "@/shared/helper";

type AcceptedItemType = {
    name: string;
    icon: any;
};

export type BinInfoType = {
    [key in BinTypeKey]: {
        name: string;
        acceptedItems: AcceptedItemType[];
    };
};

let hasShownQRFrameWalkthrough = false;

const useQRScan = () => {
    const navigation = useNavigation();
    const params = useRouteParams<typeof RouteName.Scan>();
    const { isLoading, getBinDetail } = useBinDetail();
    const { permissionState, requestPermission, onError } = useCameraPermission();
    const hasPermission = permissionState.isGranted;
    const device = useCameraDevice("back");

    const [isActive, setIsActive] = React.useState(true);
    const [isTorchOn, setIsTorchOn] = React.useState(false);
    const [isProcessingQR, setIsProcessingQR] = React.useState(false);
    const [isWalkthroughVisible, setIsWalkthroughVisible] = React.useState(false);
    const [selectedBinType, setSelectedBinType] = React.useState<BinTypeKey>("ictBulbBattery");
    const [binImage, setBinImage] = React.useState(ImageAssets.ictBulbEwasteBins);
    const [isAcknowledged, setIsAcknowledged] = React.useState(false);
    const [binId, setBinId] = React.useState<number | null>(null);

    const bottomSheetRef = React.useRef<RBSheet>(null);
    const processingTimerRef = React.useRef<NodeJS.Timeout | null>(null);
    const cameraRef = React.useRef<Camera>(null);

    const framePosition = React.useMemo(() => getFramePosition(), []);

    const binInfo = React.useMemo(() => BinTypes.binInfo, []);

    React.useEffect(() => {
        if (!hasPermission) return;

        const initWalkthrough = async () => {
            if (params?.fromWalkthrough && !hasShownQRFrameWalkthrough) {
                hasShownQRFrameWalkthrough = true;
                setIsWalkthroughVisible(true);
            }
        };

        initWalkthrough();
    }, [hasPermission, params?.fromWalkthrough]);

    useFocusEffect(
        React.useCallback(() => {
            setIsActive(true);
            setIsProcessingQR(false);

            return () => {
                setIsActive(false);
            };
        }, [])
    );

    React.useEffect(() => {
        return () => {
            if (processingTimerRef.current) {
                clearTimeout(processingTimerRef.current);
            }
        };
    }, []);

    const isPointInsideFrame = React.useCallback(
        (point: { x: number; y: number }) => {
            const padding = Math.min(fullWidth, fullHeight) * 0.1;

            return (
                point.x >= framePosition.left - padding &&
                point.x <= framePosition.left + framePosition.width + padding &&
                point.y >= framePosition.top - padding &&
                point.y <= framePosition.top + framePosition.height + padding
            );
        },
        [framePosition]
    );

    const resetScanState = React.useCallback((delay: number = 1500) => {
        if (processingTimerRef.current) {
            clearTimeout(processingTimerRef.current);
        }

        processingTimerRef.current = setTimeout(() => {
            setIsProcessingQR(false);
            setIsActive(true);
        }, delay) as unknown as NodeJS.Timeout;
    }, []);

    const safeVibrate = React.useCallback(() => {
        try {
            Vibration.vibrate(100);
        } catch (error) {
            /* empty */
        }
    }, []);

    const getBinImageForType = React.useCallback((binType: BinTypeKey) => BinTypes.getBinImageForType(binType), []);

    const fetchBinDataWithToken = React.useCallback(
        async (url: string) => {
            try {
                const response = await getBinDetail(url);

                if (!response) return;

                const binData = response;
                setBinId(binData.id);

                const binTypeKey = BinTypes.determineBinType(
                    binData.bin_type_id,
                    binData.e_waste_bin_type_id
                ) as BinTypeKey;

                setSelectedBinType(binTypeKey);
                setBinImage(getBinImageForType(binTypeKey));
                setIsAcknowledged(false);

                bottomSheetRef.current?.open();

                return response;
            } catch (error) {
                console.error("Error fetching bin data:", error);
                return null;
            }
        },
        [getBinDetail, getBinImageForType]
    );

    const processQrCode = React.useCallback(
        (scannedCode: Code) => {
            setIsProcessingQR(true);
            safeVibrate();

            if (scannedCode.value) {
                let qrValue = scannedCode.value;

                try {
                    // eslint-disable-next-line quotes
                    if (qrValue.startsWith('"') && qrValue.endsWith('"')) {
                        qrValue = JSON.parse(qrValue);
                    }
                } catch (e) {
                    console.error("Error parsing QR value:", e);
                    resetScanState(2000);
                    return;
                }

                if (typeof qrValue === "string" && qrValue.startsWith("http")) {
                    setIsActive(false);

                    fetchBinDataWithToken(qrValue).catch(() => {
                        resetScanState(2000);
                    });
                } else {
                    resetScanState(2000);
                }
            } else {
                resetScanState(2000);
            }
        },
        [fetchBinDataWithToken, resetScanState, safeVibrate]
    );

    const handleCodeScanned = React.useCallback(
        (codes: Code[]) => {
            if (codes.length > 0 && isActive && !isProcessingQR) {
                const scannedCode = codes[0];

                if (isIos) {
                    processQrCode(scannedCode);
                } else {
                    const { frame } = scannedCode;
                    const cornerPoints = (scannedCode as any).cornerPoints as { x: number; y: number }[] | undefined;

                    if (cornerPoints && cornerPoints.length > 0) {
                        const hasPointInFrame = cornerPoints.some((point) => isPointInsideFrame(point));

                        if (hasPointInFrame) {
                            processQrCode(scannedCode);
                        }
                    } else if (frame) {
                        const centerX = frame.x + frame.width / 2;
                        const centerY = frame.y + frame.height / 2;

                        if (isPointInsideFrame({ x: centerX, y: centerY })) {
                            processQrCode(scannedCode);
                        }
                    }
                }
            }
        },
        [isActive, isPointInsideFrame, isProcessingQR, processQrCode]
    );

    const handleCloseWalkthrough = React.useCallback(() => {
        setIsWalkthroughVisible(false);
    }, []);

    const handleCloseBottomSheet = React.useCallback(() => {
        setIsActive(true);
        setIsProcessingQR(false);
    }, []);

    const handleTurnOnOffLight = React.useCallback(() => {
        setIsTorchOn((prev) => !prev);
    }, []);

    const handleSelect = React.useCallback(() => {
        setIsActive(false);
        setIsProcessingQR(true);

        if (processingTimerRef.current) {
            clearTimeout(processingTimerRef.current);
            processingTimerRef.current = null;
        }

        if (bottomSheetRef.current) {
            bottomSheetRef.current.close();

            setTimeout(() => {
                setIsActive(false);
                navigation.navigate(RouteName.ScanStack, { screen: RouteName.TookPhoto, params: { binId } });
            }, 400);
        }
    }, [binId, navigation]);

    // TODO: for testing
    const nextScreen = React.useCallback(
        () => navigation.navigate(RouteName.ScanStack, { screen: RouteName.TookPhoto, params: { binId: 44 } }),
        [navigation]
    );

    return {
        isLoading,
        isActive,
        isTorchOn,
        isWalkthroughVisible,
        device,
        cameraRef,
        hasPermission,
        requestPermission,
        onError,
        handleCodeScanned,
        handleCloseWalkthrough,
        handleCloseBottomSheet,
        handleTurnOnOffLight,
        handleSelect,
        nextScreen,
        bottomSheetRef,
        binInfo,
        selectedBinType,
        binImage,
        isAcknowledged,
        setIsAcknowledged
    };
};

export default useQRScan;
