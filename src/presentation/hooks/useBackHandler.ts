import React from "react";
import { <PERSON><PERSON>, BackHandler } from "react-native";

const useBackHandler = () => {
    const backAction = React.useCallback(() => {
        Alert.alert("", "Do you want to exit the app?", [
            {
                text: "Yes",
                onPress: () => BackHandler.exitApp(),
                style: "cancel"
            },
            {
                text: "No",

                onPress: () => {}
            }
        ]);

        return true;
    }, []);

    React.useEffect(() => {
        const backHandler = BackHandler.addEventListener("hardwareBackPress", backAction);

        return () => backHandler.remove();
    }, [backAction]);
};

export default useBackHandler;
