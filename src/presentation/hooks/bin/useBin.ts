import { useBinByWasteTypeIdQueries, useBinQueries } from "@/data/queries";

const useBin = () => {
    const { getBinsByWasteTypeId, isLoading: isLoadingByWasteTypeId, binsByWasteTypeId } = useBinByWasteTypeIdQueries();
    const { bins, isLoading } = useBinQueries();

    return {
        bins,
        getBinsByWasteTypeId,
        binsByWasteTypeId,
        isLoading: isLoading || isLoadingByWasteTypeId
    };
};

export default useBin;
