import { useNavigation } from "@react-navigation/native";
import React from "react";
import { Alert, Keyboard } from "react-native";
import { OtpInputRef } from "react-native-otp-entry";
import { object } from "yup";

import { useRegisterStore } from "@/app/store";

import { useRegisterIndividualQueries, useRegisterSchoolQueries } from "@/data/queries";

import useCountDown from "../useCountDown";
import { useForm } from "../useForm";
import useRouteParams from "../useRouteParams";

import { FirebaseAuthService } from "@/data/services/firebase";
import { RouteName } from "@/shared/constants";
import { otpSchema } from "@/shared/validations";

const useVerification = () => {
    const params = useRouteParams<typeof RouteName.Verification>();

    const { isEnable, resetTime, timeFormatted } = useCountDown(120);

    const otpRef = React.useRef<OtpInputRef>(null);
    const [isLoadingOtp, setIsLoadingOtp] = React.useState(false);

    const { registerIndividual, isLoading: isLoadingRegister } = useRegisterIndividualQueries();
    const { registerSchool, isLoading: isLoadingRegisterSchool } = useRegisterSchoolQueries();
    const { request } = useRegisterStore();

    const navigation = useNavigation();

    const onSubmit = React.useCallback(
        async (values: { otp: string }, resetForm: () => void) => {
            Keyboard.dismiss();
            try {
                setIsLoadingOtp(true);
                const isVerified = await FirebaseAuthService.verifyOTP(values.otp);

                if (!isVerified) return;

                switch (params?.type) {
                    case "register": {
                        if (request?.individual) {
                            await registerIndividual(request.individual);
                        }
                        break;
                    }
                    case "registerSchool": {
                        if (request?.school) {
                            await registerSchool(request.school);
                        }
                        break;
                    }
                    case "forgotPassword": {
                        navigation.navigate(RouteName.Auth, {
                            screen: RouteName.SetNewPassword,
                            params: {
                                phoneNumber: params?.phoneNumber
                            }
                        });
                        break;
                    }
                }
                otpRef.current?.clear();
                resetForm();
            } catch (error) {
                Alert.alert("Error", `Error: ${error}`);
                /* empty */
            } finally {
                setIsLoadingOtp(false);
            }
        },
        [
            params?.type,
            params?.phoneNumber,
            request?.individual,
            request?.school,
            registerIndividual,
            registerSchool,
            navigation
        ]
    );

    const { getInputProps, handleSubmit, formik } = useForm({
        enableReinitialize: true,
        initialValues: {
            otp: ""
        },
        validationSchema: object().shape({
            otp: otpSchema
        }),
        onSubmit: (values, formikHelpers) => onSubmit(values, formikHelpers.resetForm)
    });

    const handleResendOtp = React.useCallback(async () => {
        if (!isEnable) return;

        try {
            setIsLoadingOtp(true);
            const isSent = await FirebaseAuthService.sendOTP(params?.phoneNumber);
            if (!isSent) return;
            resetTime();
            otpRef.current?.clear();
            formik.resetForm();
        } catch (error) {
            /* empty */
        } finally {
            setIsLoadingOtp(false);
        }
    }, [formik, isEnable, params?.phoneNumber, resetTime]);

    return {
        isEnable,
        timeFormatted,
        otpRef,
        isLoadingOtp,
        isLoadingRegister,
        isLoadingRegisterSchool,
        getInputProps,
        handleSubmit,
        handleResendOtp,
        params
    };
};

export default useVerification;
