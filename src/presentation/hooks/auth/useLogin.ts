import { useNavigation } from "@react-navigation/native";
import React from "react";
import { Keyboard } from "react-native";
import { object, ObjectSchema } from "yup";

import { useLoginQueries } from "@/data/queries";

import { useForm } from "../useForm";

import { RouteName } from "@/shared/constants";
import { emailSchema, passwordSchema, usernameSchema } from "@/shared/validations";

interface LoginFormValues {
    email: string;
    password: string;
    username?: string;
}

const useLogin = () => {
    const navigation = useNavigation();
    const { login, isLoading, error } = useLoginQueries();
    const [isOrganization, setIsOrganization] = React.useState(false);

    const handleForgotPassword = React.useCallback(() => {
        navigation.navigate(RouteName.ForgotPassword);
    }, [navigation]);

    const getValidationSchema = React.useCallback(() => {
        return object({
            email: emailSchema,
            ...(isOrganization ? { username: usernameSchema } : {}),
            password: passwordSchema
        }) as ObjectSchema<Partial<LoginFormValues>>;
    }, [isOrganization]);

    const { getInputProps, handleSubmit, formik } = useForm<LoginFormValues>({
        initialValues: {
            email: __DEV__ ? "<EMAIL>" : "",
            ...(isOrganization && {
                username: __DEV__ ? "username" : ""
            }),
            password: __DEV__ ? "password123" : ""
        },
        validationSchema: getValidationSchema(),
        onSubmit: async (values) => {
            await login({
                login: isOrganization ? values.username! : values.email,
                password: values.password,
                isOrganization
            });
            Keyboard.dismiss();
        }
    });

    const handleOrganization = React.useCallback(() => {
        formik.resetForm();
        setIsOrganization((prev) => !prev);
    }, [formik]);

    const titleChange = React.useMemo(() => {
        if (!isOrganization) {
            return {
                0: "Proceed as Organization",
                1: "Your email",
                2: "Enter your email"
            };
        }
        return {
            0: "Proceed as Individual",
            1: "Your username",
            2: "Enter your username "
        };
    }, [isOrganization]);

    return {
        getInputProps,
        handleSubmit,
        isLoading,
        error,
        handleForgotPassword,
        handleOrganization,
        isOrganization,
        titleChange
    };
};

export default useLogin;
