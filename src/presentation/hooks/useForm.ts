import { FormikHelpers, FormikValues, useFormik } from "formik";
import { ObjectSchema } from "yup";

interface UseFormProps<Values extends FormikValues = FormikValues> {
    initialValues: Values;
    validationSchema?: ObjectSchema<Partial<Values>>;
    onSubmit: (values: Values, formikHelpers: FormikHelpers<Values>) => void | Promise<any>;
    validateOnChange?: boolean;
    validateOnBlur?: boolean;
    enableReinitialize?: boolean;
}

export const useForm = <Values extends FormikValues = FormikValues>({
    initialValues,
    validationSchema,
    onSubmit,
    validateOnChange = true,
    validateOnBlur = true,
    enableReinitialize = false
}: UseFormProps<Values>) => {
    const formik = useFormik<Values>({
        enableReinitialize,
        initialValues,
        validationSchema,
        onSubmit,
        validateOnChange,
        validateOnBlur
    });

    const getInputProps = (fieldName: keyof Values) => ({
        fieldName: fieldName as string,
        value: formik.values[fieldName],
        error: formik.touched[fieldName] && formik.errors[fieldName],
        onChangeValue: formik.setFieldValue,
        onBlur: () => formik.setFieldTouched(fieldName as string, true)
    });

    return {
        formik,
        getInputProps,
        values: formik.values,
        errors: formik.errors,
        touched: formik.touched,
        isValid: formik.isValid,
        isSubmitting: formik.isSubmitting,
        handleSubmit: formik.handleSubmit,
        resetForm: formik.resetForm,
        setValues: formik.setValues,
        setFieldValue: formik.setFieldValue,
        setFieldTouched: formik.setFieldTouched,
        validateForm: formik.validateForm
    };
};
