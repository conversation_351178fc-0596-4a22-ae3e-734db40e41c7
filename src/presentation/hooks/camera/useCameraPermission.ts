import React from "react";
import { Camera, CameraPermissionStatus, CameraRuntimeError } from "react-native-vision-camera";

export type CameraPermissionState = {
    status: CameraPermissionStatus;
    isGranted: boolean;
};

export type CameraPermissionResult = {
    permissionState: CameraPermissionState;
    isLoading: boolean;
    requestPermission: () => Promise<boolean>;
    onError: (error: CameraRuntimeError) => void;
};

const useCameraPermission = (): CameraPermissionResult => {
    const [permissionState, setPermissionState] = React.useState<CameraPermissionState>({
        status: "not-determined",
        isGranted: false
    });

    const [isLoading, setIsLoading] = React.useState(true);
    const updatePermissionState = React.useCallback((status: CameraPermissionStatus) => {
        setPermissionState({
            status,
            isGranted: status === "granted"
        });
    }, []);

    const requestPermission = React.useCallback(async (): Promise<boolean> => {
        setIsLoading(true);
        return Camera.requestCameraPermission()
            .then((status) => {
                updatePermissionState(status);
                return status === "granted";
            })
            .catch((error) => {
                console.error("Error requesting camera permission:", error);
                updatePermissionState("denied");
                return false;
            })
            .finally(() => {
                setIsLoading(false);
            });
    }, [updatePermissionState]);

    React.useEffect(() => {
        const checkPermission = (): void => {
            setIsLoading(true);

            Promise.resolve(Camera.getCameraPermissionStatus())
                .then((status) => {
                    updatePermissionState(status);
                })
                .catch((error) => {
                    console.error("Error checking camera permission:", error);
                    updatePermissionState("denied");
                })
                .finally(() => {
                    setIsLoading(false);
                });
        };

        checkPermission();
    }, [updatePermissionState]);

    const onError = React.useCallback((error: CameraRuntimeError) => {
        console.error(`Camera error: ${error.code}`, error.message);
    }, []);

    return {
        permissionState,
        isLoading,
        requestPermission,
        onError
    };
};

export default useCameraPermission;
