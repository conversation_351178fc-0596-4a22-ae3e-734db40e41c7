import React from "react";

const useCountDown = (seconds: number) => {
    const [time, setTime] = React.useState(seconds);
    const [isEnable, setIsEnable] = React.useState<boolean>(false);

    React.useEffect(() => {
        if (!time) {
            setIsEnable(true);
            return;
        }
        setIsEnable(false);

        const intervalId = setInterval(() => {
            setTime(time - 1);
        }, 1000);

        return () => clearInterval(intervalId);
    }, [time]);

    const resetTime = React.useCallback(() => {
        setTime(seconds);
    }, [seconds]);

    const timeFormatted = React.useMemo(() => {
        const mins = Math.floor(time / 60);
        const secs = time % 60;
        return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    }, [time]);

    return { time, isEnable, resetTime, timeFormatted };
};

export default useCountDown;
