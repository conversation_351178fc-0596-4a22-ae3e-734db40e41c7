import { useNavigation } from "@react-navigation/native";
import React from "react";

import { HttpClient } from "@/data/services/httpClient";
import { RouteName } from "@/shared/constants";

const useStartApp = () => {
    const navigation = useNavigation();
    React.useEffect(() => {
        const init = async () => {
            const isSuccess = await HttpClient.getTokenService().refreshToken();
            if (isSuccess) {
                navigation.replace(RouteName.Bottom);
            } else {
                navigation.replace(RouteName.Welcome);
            }
        };

        init();
    }, [navigation]);
};

export default useStartApp;
